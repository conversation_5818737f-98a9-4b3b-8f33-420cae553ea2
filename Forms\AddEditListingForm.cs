using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Forms;
using SeoulStayApp.Data;
using SeoulStayApp.Models;
using SeoulStayApp.Services;

namespace SeoulStayApp.Forms
{
    public partial class AddEditListingForm : Form
    {
        private readonly ItemRepository _itemRepository;
        private readonly User _currentUser;
        private Item? _editingItem;
        private bool _isEditMode;

        // Tab Control
        private TabControl _mainTabControl;

        // Listing Details Tab Controls
        private ComboBox _typeComboBox;
        private TextBox _titleTextBox;
        private NumericUpDown _capacityNumeric;
        private NumericUpDown _bedsNumeric;
        private NumericUpDown _bedroomsNumeric;
        private NumericUpDown _bathroomsNumeric;
        private TextBox _approximateAddressTextBox;
        private TextBox _exactAddressTextBox;
        private TextBox _descriptionTextBox;
        private TextBox _hostRulesTextBox;
        private NumericUpDown _minimumNightsNumeric;
        private NumericUpDown _maximumNightsNumeric;

        // Amenities Tab Controls
        private CheckedListBox _amenitiesCheckedListBox;

        // Distance to Attraction Tab Controls
        private DataGridView _attractionsDataGridView;

        // Navigation Buttons
        private Button _nextButton;
        private Button _closeFinishButton;

        public AddEditListingForm(User currentUser, Item? editingItem = null)
        {
            _currentUser = currentUser;
            _editingItem = editingItem;
            _isEditMode = editingItem != null;

            _itemRepository = new ItemRepository();

            InitializeComponent();
            LoadFormData();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties
            this.Text = _isEditMode ? "Seoul Stay - Edit Listing" : "Seoul Stay - Add Listing";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.White;
            this.Font = new Font("Segoe UI", 9);

            // Main TabControl
            _mainTabControl = new TabControl
            {
                Location = new Point(20, 20),
                Size = new Size(740, 500),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                Appearance = TabAppearance.FlatButtons,
                SizeMode = TabSizeMode.Fixed,
                ItemSize = new Size(140, 30)
            };

            CreateListingDetailsTab();
            CreateAmenitiesTab();
            CreateDistanceToAttractionTab();

            this.Controls.Add(_mainTabControl);

            // Navigation Buttons
            _nextButton = new Button
            {
                Name = "nextButton",
                Text = "Next",
                Location = new Point(580, 540),
                Size = new Size(80, 35),
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            _nextButton.FlatAppearance.BorderSize = 0;
            _nextButton.Click += NextButton_Click;
            this.Controls.Add(_nextButton);

            _closeFinishButton = new Button
            {
                Name = "closeFinishButton",
                Text = _isEditMode ? "Close" : "Finish",
                Location = new Point(680, 540),
                Size = new Size(80, 35),
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand
            };
            _closeFinishButton.FlatAppearance.BorderSize = 0;
            _closeFinishButton.Click += CloseFinishButton_Click;
            this.Controls.Add(_closeFinishButton);

            this.ResumeLayout(false);
        }

        private void CreateListingDetailsTab()
        {
            var listingDetailsTab = new TabPage("Listing Details");
            listingDetailsTab.BackColor = Color.White;
            listingDetailsTab.Padding = new Padding(15);

            // Type dropdown
            var typeLabel = new Label
            {
                Text = "Type:",
                Location = new Point(20, 25),
                Size = new Size(60, 20),
                Font = new Font("Segoe UI", 10)
            };
            listingDetailsTab.Controls.Add(typeLabel);

            _typeComboBox = new ComboBox
            {
                Location = new Point(90, 22),
                Size = new Size(200, 25),
                DropDownStyle = ComboBoxStyle.DropDownList,
                Font = new Font("Segoe UI", 9)
            };
            listingDetailsTab.Controls.Add(_typeComboBox);

            // Title
            var titleLabel = new Label
            {
                Text = "Title:",
                Location = new Point(400, 25),
                Size = new Size(60, 20),
                Font = new Font("Segoe UI", 10)
            };
            listingDetailsTab.Controls.Add(titleLabel);

            _titleTextBox = new TextBox
            {
                Location = new Point(470, 22),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 9)
            };
            listingDetailsTab.Controls.Add(_titleTextBox);

            // Capacity and bed info row
            var capacityLabel = new Label
            {
                Text = "Capacity:",
                Location = new Point(20, 65),
                Size = new Size(60, 20),
                Font = new Font("Segoe UI", 10)
            };
            listingDetailsTab.Controls.Add(capacityLabel);

            _capacityNumeric = new NumericUpDown
            {
                Location = new Point(90, 62),
                Size = new Size(60, 25),
                Minimum = 1,
                Maximum = 50,
                Value = 1
            };
            listingDetailsTab.Controls.Add(_capacityNumeric);

            CreateNumericFields(listingDetailsTab);
            CreateAddressFields(listingDetailsTab);
            CreateDescriptionFields(listingDetailsTab);
            CreateReservationFields(listingDetailsTab);

            _mainTabControl.TabPages.Add(listingDetailsTab);
        }

        private void CreateNumericFields(TabPage parent)
        {
            // Number of Beds
            var bedsLabel = new Label
            {
                Text = "Number of Beds:",
                Location = new Point(180, 65),
                Size = new Size(110, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(bedsLabel);

            _bedsNumeric = new NumericUpDown
            {
                Location = new Point(300, 62),
                Size = new Size(60, 25),
                Minimum = 1,
                Maximum = 20,
                Value = 1
            };
            parent.Controls.Add(_bedsNumeric);

            // Number of Bedrooms
            var bedroomsLabel = new Label
            {
                Text = "Number of Bedrooms:",
                Location = new Point(380, 65),
                Size = new Size(140, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(bedroomsLabel);

            _bedroomsNumeric = new NumericUpDown
            {
                Location = new Point(530, 62),
                Size = new Size(60, 25),
                Minimum = 1,
                Maximum = 10,
                Value = 1
            };
            parent.Controls.Add(_bedroomsNumeric);

            // Number of Bathrooms
            var bathroomsLabel = new Label
            {
                Text = "Number of Bathrooms:",
                Location = new Point(600, 65),
                Size = new Size(140, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(bathroomsLabel);

            _bathroomsNumeric = new NumericUpDown
            {
                Location = new Point(680, 62),
                Size = new Size(60, 25),
                Minimum = 1,
                Maximum = 10,
                Value = 1
            };
            parent.Controls.Add(_bathroomsNumeric);
        }

        private void CreateAddressFields(TabPage parent)
        {
            // Approximate Address
            var approxAddressLabel = new Label
            {
                Text = "Approximate Address:",
                Location = new Point(20, 105),
                Size = new Size(140, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(approxAddressLabel);

            _approximateAddressTextBox = new TextBox
            {
                Location = new Point(170, 102),
                Size = new Size(550, 25),
                Font = new Font("Segoe UI", 9)
            };
            parent.Controls.Add(_approximateAddressTextBox);

            // Exact Address
            var exactAddressLabel = new Label
            {
                Text = "Exact Address:",
                Location = new Point(20, 145),
                Size = new Size(100, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(exactAddressLabel);

            _exactAddressTextBox = new TextBox
            {
                Location = new Point(130, 142),
                Size = new Size(590, 25),
                Font = new Font("Segoe UI", 9)
            };
            parent.Controls.Add(_exactAddressTextBox);
        }

        private void CreateDescriptionFields(TabPage parent)
        {
            // Description
            var descriptionLabel = new Label
            {
                Text = "Description:",
                Location = new Point(20, 185),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(descriptionLabel);

            _descriptionTextBox = new TextBox
            {
                Location = new Point(110, 182),
                Size = new Size(610, 60),
                Font = new Font("Segoe UI", 9),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            parent.Controls.Add(_descriptionTextBox);

            // Host Rules
            var hostRulesLabel = new Label
            {
                Text = "Host Rules:",
                Location = new Point(20, 260),
                Size = new Size(80, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(hostRulesLabel);

            _hostRulesTextBox = new TextBox
            {
                Location = new Point(110, 257),
                Size = new Size(610, 60),
                Font = new Font("Segoe UI", 9),
                Multiline = true,
                ScrollBars = ScrollBars.Vertical
            };
            parent.Controls.Add(_hostRulesTextBox);
        }

        private void CreateReservationFields(TabPage parent)
        {
            // Reservation Time (Nights)
            var reservationLabel = new Label
            {
                Text = "Reservation Time (Nights):",
                Location = new Point(20, 340),
                Size = new Size(160, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(reservationLabel);

            var minimumLabel = new Label
            {
                Text = "Minimum:",
                Location = new Point(200, 340),
                Size = new Size(70, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(minimumLabel);

            _minimumNightsNumeric = new NumericUpDown
            {
                Location = new Point(280, 337),
                Size = new Size(60, 25),
                Minimum = 1,
                Maximum = 365,
                Value = 1
            };
            parent.Controls.Add(_minimumNightsNumeric);

            var maximumLabel = new Label
            {
                Text = "Maximum:",
                Location = new Point(360, 340),
                Size = new Size(70, 20),
                Font = new Font("Segoe UI", 10)
            };
            parent.Controls.Add(maximumLabel);

            _maximumNightsNumeric = new NumericUpDown
            {
                Location = new Point(440, 337),
                Size = new Size(60, 25),
                Minimum = 1,
                Maximum = 365,
                Value = 30
            };
            parent.Controls.Add(_maximumNightsNumeric);
        }

        private void CreateAmenitiesTab()
        {
            var amenitiesTab = new TabPage("Amenities");
            amenitiesTab.BackColor = Color.White;
            amenitiesTab.Padding = new Padding(15);

            // Choose Available Amenities label
            var amenitiesLabel = new Label
            {
                Text = "Choose Available Amenities:",
                Location = new Point(20, 25),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 11, FontStyle.Bold)
            };
            amenitiesTab.Controls.Add(amenitiesLabel);

            // Amenities CheckedListBox - matches wireframe exactly
            _amenitiesCheckedListBox = new CheckedListBox
            {
                Location = new Point(20, 55),
                Size = new Size(400, 350),
                Font = new Font("Segoe UI", 10),
                CheckOnClick = true,
                BorderStyle = BorderStyle.FixedSingle,
                BackColor = Color.White
            };
            amenitiesTab.Controls.Add(_amenitiesCheckedListBox);

            _mainTabControl.TabPages.Add(amenitiesTab);
        }

        private void CreateDistanceToAttractionTab()
        {
            var distanceTab = new TabPage("Distance to Attraction");
            distanceTab.BackColor = Color.White;
            distanceTab.Padding = new Padding(15);

            // Distance to Attractions DataGridView
            _attractionsDataGridView = new DataGridView
            {
                Location = new Point(20, 25),
                Size = new Size(680, 400),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                AllowUserToAddRows = false,
                AllowUserToDeleteRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(52, 73, 94),
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 10, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleLeft
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 9),
                    BackColor = Color.White,
                    ForeColor = Color.Black
                },
                ColumnHeadersHeight = 35,
                RowTemplate = { Height = 30 }
            };
            distanceTab.Controls.Add(_attractionsDataGridView);

            _mainTabControl.TabPages.Add(distanceTab);
        }

        private async void LoadFormData()
        {
            try
            {
                // Load service types for Type dropdown using ItemRepository
                var itemTypes = await _itemRepository.GetItemTypesAsync();
                _typeComboBox.DataSource = itemTypes;
                _typeComboBox.DisplayMember = "Name";
                _typeComboBox.ValueMember = "ID";

                // Load amenities for checklist (mock data for now)
                LoadMockAmenities();

                // Load attractions for distance grid (mock data for now)
                LoadMockAttractions();

                // If editing, populate form with existing data
                if (_isEditMode && _editingItem != null)
                {
                    await PopulateFormForEditing();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading form data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void LoadMockAmenities()
        {
            // Mock amenities data matching the wireframe
            var amenities = new[]
            {
                "TV",
                "Music Player",
                "BBQ Grill",
                "Smoke alarm",
                "WiFi"
            };

            _amenitiesCheckedListBox.Items.Clear();
            foreach (var amenity in amenities)
            {
                _amenitiesCheckedListBox.Items.Add(amenity, false);
            }
        }

        private void LoadMockAttractions()
        {
            // Clear any existing data source
            _attractionsDataGridView.DataSource = null;
            _attractionsDataGridView.Columns.Clear();

            // Create columns manually to avoid binding issues
            var attractionColumn = new DataGridViewTextBoxColumn
            {
                Name = "AttractionName",
                HeaderText = "Attraction",
                ReadOnly = true,
                FillWeight = 50
            };
            _attractionsDataGridView.Columns.Add(attractionColumn);

            var distanceColumn = new DataGridViewTextBoxColumn
            {
                Name = "Distance",
                HeaderText = "Distance",
                ReadOnly = false,
                FillWeight = 25
            };
            _attractionsDataGridView.Columns.Add(distanceColumn);

            var unitColumn = new DataGridViewTextBoxColumn
            {
                Name = "Unit",
                HeaderText = "Unit",
                ReadOnly = true,
                FillWeight = 25
            };
            _attractionsDataGridView.Columns.Add(unitColumn);

            // Add hidden ID column
            var idColumn = new DataGridViewTextBoxColumn
            {
                Name = "AttractionId",
                HeaderText = "ID",
                Visible = false
            };
            _attractionsDataGridView.Columns.Add(idColumn);

            // Add mock data rows
            var attractions = new[]
            {
                new { AttractionId = 1, AttractionName = "Gyeongbokgung Palace", Distance = "0.0", Unit = "km" },
                new { AttractionId = 2, AttractionName = "Myeongdong Shopping District", Distance = "0.0", Unit = "km" },
                new { AttractionId = 3, AttractionName = "Hongdae Area", Distance = "0.0", Unit = "km" },
                new { AttractionId = 4, AttractionName = "Gangnam District", Distance = "0.0", Unit = "km" },
                new { AttractionId = 5, AttractionName = "Itaewon", Distance = "0.0", Unit = "km" }
            };

            foreach (var attraction in attractions)
            {
                var row = new DataGridViewRow();
                row.CreateCells(_attractionsDataGridView);
                row.Cells[0].Value = attraction.AttractionName; // AttractionName column
                row.Cells[1].Value = attraction.Distance; // Distance column
                row.Cells[2].Value = attraction.Unit; // Unit column
                row.Cells[3].Value = attraction.AttractionId; // AttractionId column
                _attractionsDataGridView.Rows.Add(row);
            }
        }



        private async Task PopulateFormForEditing()
        {
            if (_editingItem == null) return;

            try
            {
                // Populate basic fields
                _titleTextBox.Text = _editingItem.Title;
                _capacityNumeric.Value = _editingItem.Capacity;
                _bedsNumeric.Value = _editingItem.NumberOfBeds;
                _bedroomsNumeric.Value = _editingItem.NumberOfBedrooms;
                _bathroomsNumeric.Value = _editingItem.NumberOfBathrooms;
                _approximateAddressTextBox.Text = _editingItem.ApproximateAddress ?? "";
                _exactAddressTextBox.Text = _editingItem.ExactAddress ?? "";
                _descriptionTextBox.Text = _editingItem.Description ?? "";
                _hostRulesTextBox.Text = _editingItem.HostRules ?? "";
                _minimumNightsNumeric.Value = _editingItem.MinimumNights;
                _maximumNightsNumeric.Value = _editingItem.MaximumNights;

                // Set item type
                _typeComboBox.SelectedValue = _editingItem.ItemTypeID;

                // For now, amenities and attractions will use default values
                // In a full implementation, these would be loaded from the database
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading item data: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void NextButton_Click(object? sender, EventArgs e)
        {
            // Navigate to next tab or validate current tab
            int currentIndex = _mainTabControl.SelectedIndex;
            if (currentIndex < _mainTabControl.TabCount - 1)
            {
                // Validate current tab before moving
                if (ValidateCurrentTab())
                {
                    _mainTabControl.SelectedIndex = currentIndex + 1;
                    UpdateNavigationButtons();
                }
            }
        }

        private async void CloseFinishButton_Click(object? sender, EventArgs e)
        {
            if (_isEditMode)
            {
                // Close without saving changes
                var result = MessageBox.Show("Close without saving changes?", "Confirm Close",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    this.DialogResult = DialogResult.Cancel;
                    this.Close();
                }
            }
            else
            {
                // Finish - save the new listing
                if (await SaveListing())
                {
                    MessageBox.Show("Listing saved successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
            }
        }

        private bool ValidateCurrentTab()
        {
            switch (_mainTabControl.SelectedIndex)
            {
                case 0: // Listing Details
                    if (string.IsNullOrWhiteSpace(_titleTextBox.Text))
                    {
                        MessageBox.Show("Please enter a title for the listing.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        _titleTextBox.Focus();
                        return false;
                    }
                    if (_typeComboBox.SelectedValue == null)
                    {
                        MessageBox.Show("Please select a property type.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        _typeComboBox.Focus();
                        return false;
                    }
                    if (_minimumNightsNumeric.Value > _maximumNightsNumeric.Value)
                    {
                        MessageBox.Show("Minimum nights cannot be greater than maximum nights.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        _minimumNightsNumeric.Focus();
                        return false;
                    }
                    break;
            }
            return true;
        }

        private void UpdateNavigationButtons()
        {
            int currentIndex = _mainTabControl.SelectedIndex;
            int lastIndex = _mainTabControl.TabCount - 1;

            if (currentIndex == lastIndex)
            {
                _nextButton.Visible = false;
                _closeFinishButton.Text = _isEditMode ? "Save & Close" : "Finish";
            }
            else
            {
                _nextButton.Visible = true;
                _closeFinishButton.Text = _isEditMode ? "Close" : "Cancel";
            }
        }

        private async Task<bool> SaveListing()
        {
            try
            {
                // Validate all tabs
                for (int i = 0; i < _mainTabControl.TabCount; i++)
                {
                    _mainTabControl.SelectedIndex = i;
                    if (!ValidateCurrentTab())
                    {
                        return false;
                    }
                }

                Item item;
                if (_isEditMode && _editingItem != null)
                {
                    item = _editingItem;
                }
                else
                {
                    item = new Item
                    {
                        UserID = _currentUser.ID,
                        GUID = Guid.NewGuid(),
                        AreaID = 1 // Default area - in a full implementation this would be selected
                    };
                }

                // Update item properties
                item.Title = _titleTextBox.Text.Trim();
                item.ItemTypeID = (long)_typeComboBox.SelectedValue;
                item.Capacity = (int)_capacityNumeric.Value;
                item.NumberOfBeds = (int)_bedsNumeric.Value;
                item.NumberOfBedrooms = (int)_bedroomsNumeric.Value;
                item.NumberOfBathrooms = (int)_bathroomsNumeric.Value;
                item.ApproximateAddress = _approximateAddressTextBox.Text.Trim();
                item.ExactAddress = _exactAddressTextBox.Text.Trim();
                item.Description = _descriptionTextBox.Text.Trim();
                item.HostRules = _hostRulesTextBox.Text.Trim();
                item.MinimumNights = (int)_minimumNightsNumeric.Value;
                item.MaximumNights = (int)_maximumNightsNumeric.Value;

                // Save or update item
                if (_isEditMode)
                {
                    await _itemRepository.UpdateItemAsync(item);
                }
                else
                {
                    item.ID = await _itemRepository.AddItemAsync(item);
                }

                // Note: Amenities and attraction distances would be saved here
                // in a full implementation with proper repository classes

                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving listing: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }


    }
}
