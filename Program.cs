using SeoulStayApp.Forms;
using SeoulStayApp.Data;
using System;
using System.Windows.Forms;

namespace SeoulStayApp;

static class Program
{
    /// <summary>
    ///  The main entry point for the application.
    /// </summary>
    [STAThread]
    static void Main()
    {
        // To customize application configuration such as set high DPI settings or default font,
        // see https://aka.ms/applicationconfiguration.
        ApplicationConfiguration.Initialize();

        // Test database connection first
        if (!DatabaseConnection.TestConnection())
        {
            MessageBox.Show("Cannot connect to the database. Please check your connection settings.",
                "Database Connection Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            return;
        }

        // Seed initial data
        try
        {
            DataSeeder.SeedAllAsync().Wait();
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error initializing database: {ex.Message}",
                "Database Initialization Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        // Start with new login form
        while (true)
        {
            var loginForm = new NewLoginForm();
            var loginResult = loginForm.ShowDialog();

            if (loginResult == DialogResult.OK && loginForm.LoggedInUser != null)
            {
                // User logged in successfully, show management form
                var managementForm = new ManagementForm(loginForm.LoggedInUser);
                var managementResult = managementForm.ShowDialog();

                // If user logs out, continue the loop to show login again
                if (managementResult == DialogResult.Cancel)
                {
                    continue;
                }
                else
                {
                    // User closed the management form, exit application
                    break;
                }
            }
            else
            {
                // User closed login form or cancelled, exit application
                break;
            }
        }
    }
}