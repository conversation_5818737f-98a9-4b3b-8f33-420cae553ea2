using BCrypt.Net;
using System;
using System.Security.Cryptography;
using System.Text;

namespace SeoulStayApp.Services
{
    public static class SecurityService
    {
        /// <summary>
        /// Hashes a password using BCrypt
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <returns>Hashed password</returns>
        public static string HashPassword(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                throw new ArgumentException("Password cannot be null or empty", nameof(password));
            
            return BCrypt.Net.BCrypt.HashPassword(password, BCrypt.Net.BCrypt.GenerateSalt(12));
        }
        
        /// <summary>
        /// Verifies a password against its hash
        /// </summary>
        /// <param name="password">Plain text password</param>
        /// <param name="hashedPassword">Hashed password from database</param>
        /// <returns>True if password matches</returns>
        public static bool VerifyPassword(string password, string hashedPassword)
        {
            if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(hashedPassword))
                return false;
            
            try
            {
                return BCrypt.Net.BCrypt.Verify(password, hashedPassword);
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Generates a secure random token for session management
        /// </summary>
        /// <returns>Base64 encoded token</returns>
        public static string GenerateSecureToken()
        {
            using var rng = RandomNumberGenerator.Create();
            var tokenBytes = new byte[32];
            rng.GetBytes(tokenBytes);
            return Convert.ToBase64String(tokenBytes);
        }
        
        /// <summary>
        /// Sanitizes input to prevent SQL injection
        /// </summary>
        /// <param name="input">User input</param>
        /// <returns>Sanitized input</returns>
        public static string SanitizeInput(string input)
        {
            if (string.IsNullOrEmpty(input))
                return string.Empty;
            
            // Remove potentially dangerous characters
            var dangerous = new[] { "'", "\"", ";", "--", "/*", "*/", "xp_", "sp_", "DROP", "DELETE", "INSERT", "UPDATE", "EXEC", "EXECUTE" };
            
            var sanitized = input;
            foreach (var danger in dangerous)
            {
                sanitized = sanitized.Replace(danger, "", StringComparison.OrdinalIgnoreCase);
            }
            
            return sanitized.Trim();
        }
        
        /// <summary>
        /// Validates password strength
        /// </summary>
        /// <param name="password">Password to validate</param>
        /// <returns>Validation result with message</returns>
        public static (bool IsValid, string Message) ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return (false, "Password cannot be empty");
            
            if (password.Length < 8)
                return (false, "Password must be at least 8 characters long");
            
            if (password.Length > 128)
                return (false, "Password cannot exceed 128 characters");
            
            bool hasUpper = false, hasLower = false, hasDigit = false, hasSpecial = false;
            
            foreach (char c in password)
            {
                if (char.IsUpper(c)) hasUpper = true;
                else if (char.IsLower(c)) hasLower = true;
                else if (char.IsDigit(c)) hasDigit = true;
                else if (!char.IsLetterOrDigit(c)) hasSpecial = true;
            }
            
            var missing = new List<string>();
            if (!hasUpper) missing.Add("uppercase letter");
            if (!hasLower) missing.Add("lowercase letter");
            if (!hasDigit) missing.Add("number");
            if (!hasSpecial) missing.Add("special character");
            
            if (missing.Count > 0)
                return (false, $"Password must contain at least one: {string.Join(", ", missing)}");
            
            return (true, "Password is strong");
        }
        
        /// <summary>
        /// Validates username format
        /// </summary>
        /// <param name="username">Username to validate</param>
        /// <returns>Validation result</returns>
        public static (bool IsValid, string Message) ValidateUsername(string username)
        {
            if (string.IsNullOrWhiteSpace(username))
                return (false, "Username cannot be empty");
            
            if (username.Length < 3)
                return (false, "Username must be at least 3 characters long");
            
            if (username.Length > 50)
                return (false, "Username cannot exceed 50 characters");
            
            // Only allow alphanumeric characters and underscores
            if (!System.Text.RegularExpressions.Regex.IsMatch(username, @"^[a-zA-Z0-9_]+$"))
                return (false, "Username can only contain letters, numbers, and underscores");
            
            return (true, "Username is valid");
        }
    }
}
