using SeoulStayApp.Data;
using SeoulStayApp.Models;
using SeoulStayApp.Services;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace SeoulStayApp.Forms
{
    public partial class ManagementForm : Form
    {
        private User _currentUser;
        private ItemRepository _itemRepository;
        private TabControl _mainTabControl = null!;
        private DataGridView _travelerDataGridView = null!;
        private DataGridView _ownerDataGridView = null!;
        
        public ManagementForm(User user)
        {
            _currentUser = user;
            _itemRepository = new ItemRepository();
            InitializeComponent();
            LoadData();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Form properties - matches wireframe exactly
            this.Text = "Seoul Stay - Management";
            this.Size = new Size(1000, 700);
            this.MinimumSize = new Size(900, 650);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.KeyPreview = true;
            this.KeyDown += ManagementForm_KeyDown;
            this.BackColor = Color.FromArgb(248, 250, 252); // Modern light blue-gray

            // Logo Panel - modern design with gradient effect
            var logoPanel = new Panel
            {
                Location = new Point(350, 20),
                Size = new Size(300, 70),
                BackColor = Color.FromArgb(59, 130, 246), // Modern blue
                BorderStyle = BorderStyle.None
            };
            logoPanel.Paint += (s, e) => {
                // Add subtle shadow effect
                using (var brush = new SolidBrush(Color.FromArgb(30, 0, 0, 0)))
                {
                    e.Graphics.FillRectangle(brush, 2, 2, logoPanel.Width, logoPanel.Height);
                }
                // Main background
                using (var brush = new SolidBrush(Color.FromArgb(59, 130, 246)))
                {
                    e.Graphics.FillRectangle(brush, 0, 0, logoPanel.Width - 2, logoPanel.Height - 2);
                }
            };
            this.Controls.Add(logoPanel);

            var logoLabel = new Label
            {
                Text = "🏠 SEOUL STAY",
                Font = new Font("Segoe UI", 18, FontStyle.Bold),
                TextAlign = ContentAlignment.MiddleCenter,
                Location = new Point(0, 0),
                Size = new Size(300, 70),
                ForeColor = Color.White,
                BackColor = Color.Transparent
            };
            logoPanel.Controls.Add(logoLabel);
            
            // Role buttons - positioned like wireframe
            var travelerButton = new Button
            {
                Name = "travelerButton",
                Text = "🧳 I'm Traveler",
                Location = new Point(100, 110),
                Size = new Size(150, 45),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(34, 197, 94), // Modern green
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            travelerButton.FlatAppearance.BorderSize = 0;
            travelerButton.MouseEnter += (s, e) => travelerButton.BackColor = Color.FromArgb(22, 163, 74);
            travelerButton.MouseLeave += (s, e) => travelerButton.BackColor = Color.FromArgb(34, 197, 94);
            travelerButton.Click += (s, e) => SwitchToTravelerView();
            this.Controls.Add(travelerButton);

            var ownerButton = new Button
            {
                Name = "ownerButton",
                Text = "🏠 I'm Owner / Manager",
                Location = new Point(270, 110),
                Size = new Size(180, 45),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(168, 85, 247), // Modern purple
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            ownerButton.FlatAppearance.BorderSize = 0;
            ownerButton.MouseEnter += (s, e) => ownerButton.BackColor = Color.FromArgb(147, 51, 234);
            ownerButton.MouseLeave += (s, e) => ownerButton.BackColor = Color.FromArgb(168, 85, 247);
            ownerButton.Click += (s, e) => SwitchToOwnerView();
            this.Controls.Add(ownerButton);

            // Search Button - modern design
            var searchButton = new Button
            {
                Name = "searchButton",
                Text = "🔍 Advanced Search",
                Location = new Point(470, 110),
                Size = new Size(150, 45),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(59, 130, 246), // Modern blue
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            searchButton.FlatAppearance.BorderSize = 0;
            searchButton.MouseEnter += (s, e) => searchButton.BackColor = Color.FromArgb(37, 99, 235);
            searchButton.MouseLeave += (s, e) => searchButton.BackColor = Color.FromArgb(59, 130, 246);
            searchButton.Click += SearchButton_Click;
            this.Controls.Add(searchButton);
            
            // Logout and Exit buttons - modern design (top right)
            var logoutButton = new Button
            {
                Name = "logoutButton",
                Text = "🚪 Log out",
                Location = new Point(640, 110),
                Size = new Size(100, 45),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(239, 68, 68), // Modern red
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            logoutButton.FlatAppearance.BorderSize = 0;
            logoutButton.MouseEnter += (s, e) => logoutButton.BackColor = Color.FromArgb(220, 38, 38);
            logoutButton.MouseLeave += (s, e) => logoutButton.BackColor = Color.FromArgb(239, 68, 68);
            logoutButton.Click += LogoutButton_Click;
            this.Controls.Add(logoutButton);

            var exitButton = new Button
            {
                Name = "exitButton",
                Text = "❌ Exit",
                Location = new Point(760, 110),
                Size = new Size(100, 45),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(75, 85, 99), // Modern gray
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.MouseEnter += (s, e) => exitButton.BackColor = Color.FromArgb(55, 65, 81);
            exitButton.MouseLeave += (s, e) => exitButton.BackColor = Color.FromArgb(75, 85, 99);
            exitButton.Click += (s, e) => ExitApplication();
            this.Controls.Add(exitButton);
            
            // Main TabControl - Professional styling to match wireframe
            _mainTabControl = new TabControl
            {
                Location = new Point(50, 140),
                Size = new Size(900, 480),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                Appearance = TabAppearance.FlatButtons,
                SizeMode = TabSizeMode.Fixed,
                ItemSize = new Size(150, 35)
            };

            // Traveler Tab - Search functionality
            var travelerTab = new TabPage("I'm Traveler");
            travelerTab.BackColor = Color.White;
            travelerTab.Padding = new Padding(10);

            // Search box for Traveler tab
            var searchTextBox = new TextBox
            {
                Name = "searchTextBox",
                Location = new Point(15, 15),
                Size = new Size(850, 30),
                Font = new Font("Segoe UI", 11),
                PlaceholderText = "Search destination or Listing Title or Attraction",
                BorderStyle = BorderStyle.FixedSingle
            };
            searchTextBox.TextChanged += SearchTextBox_TextChanged;
            travelerTab.Controls.Add(searchTextBox);

            // Traveler DataGridView
            _travelerDataGridView = new DataGridView
            {
                Name = "travelerDataGridView",
                Location = new Point(15, 55),
                Size = new Size(850, 360),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.FromArgb(249, 250, 251), // Light gray background
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(59, 130, 246), // Modern blue header
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(8, 8, 8, 8)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 10),
                    SelectionBackColor = Color.FromArgb(34, 197, 94), // Modern green selection
                    SelectionForeColor = Color.White,
                    BackColor = Color.White,
                    ForeColor = Color.FromArgb(31, 41, 55), // Dark gray text
                    Padding = new Padding(8, 6, 8, 6)
                },
                AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(248, 250, 252), // Alternating row color
                    ForeColor = Color.FromArgb(31, 41, 55)
                },
                ColumnHeadersHeight = 45,
                RowTemplate = { Height = 40 },
                GridColor = Color.FromArgb(229, 231, 235), // Light border color
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal
            };
            travelerTab.Controls.Add(_travelerDataGridView);
            _mainTabControl.TabPages.Add(travelerTab);

            // Owner/Manager Tab - Add Listing functionality
            var ownerTab = new TabPage("I'm Owner / Manager");
            ownerTab.BackColor = Color.White;
            ownerTab.Padding = new Padding(10);

            // Add Listing button for Owner tab
            var addListingButton = new Button
            {
                Name = "addListingButton",
                Text = "➕ Add New Listing",
                Location = new Point(15, 15),
                Size = new Size(160, 40),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(34, 197, 94), // Modern green
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                Cursor = Cursors.Hand,
                TextAlign = ContentAlignment.MiddleCenter
            };
            addListingButton.FlatAppearance.BorderSize = 0;
            addListingButton.MouseEnter += (s, e) => addListingButton.BackColor = Color.FromArgb(22, 163, 74);
            addListingButton.MouseLeave += (s, e) => addListingButton.BackColor = Color.FromArgb(34, 197, 94);
            addListingButton.Click += AddListingButton_Click;
            ownerTab.Controls.Add(addListingButton);

            // Owner DataGridView
            _ownerDataGridView = new DataGridView
            {
                Name = "ownerDataGridView",
                Location = new Point(15, 60),
                Size = new Size(850, 355),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect,
                BackgroundColor = Color.FromArgb(249, 250, 251), // Light gray background
                BorderStyle = BorderStyle.None,
                ColumnHeadersDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(168, 85, 247), // Modern purple header
                    ForeColor = Color.White,
                    Font = new Font("Segoe UI", 11, FontStyle.Bold),
                    Alignment = DataGridViewContentAlignment.MiddleCenter,
                    Padding = new Padding(8, 8, 8, 8)
                },
                DefaultCellStyle = new DataGridViewCellStyle
                {
                    Font = new Font("Segoe UI", 10),
                    SelectionBackColor = Color.FromArgb(168, 85, 247), // Modern purple selection
                    SelectionForeColor = Color.White,
                    BackColor = Color.White,
                    ForeColor = Color.FromArgb(31, 41, 55), // Dark gray text
                    Padding = new Padding(8, 6, 8, 6)
                },
                AlternatingRowsDefaultCellStyle = new DataGridViewCellStyle
                {
                    BackColor = Color.FromArgb(248, 250, 252), // Alternating row color
                    ForeColor = Color.FromArgb(31, 41, 55)
                },
                ColumnHeadersHeight = 45,
                RowTemplate = { Height = 40 },
                GridColor = Color.FromArgb(229, 231, 235), // Light border color
                CellBorderStyle = DataGridViewCellBorderStyle.SingleHorizontal
            };
            ownerTab.Controls.Add(_ownerDataGridView);
            _mainTabControl.TabPages.Add(ownerTab);

            this.Controls.Add(_mainTabControl);

            // Status label - matches wireframe
            var statusLabel = new Label
            {
                Name = "statusLabel",
                Text = "3 items found.",
                Location = new Point(50, 580),
                Size = new Size(900, 30),
                Font = new Font("Segoe UI", 10),
                BackColor = Color.FromArgb(236, 240, 241),
                ForeColor = Color.FromArgb(52, 73, 94),
                BorderStyle = BorderStyle.FixedSingle,
                TextAlign = ContentAlignment.MiddleLeft,
                Padding = new Padding(10, 0, 0, 0)
            };
            this.Controls.Add(statusLabel);

            // Add resize event handler for responsive design
            this.Resize += ManagementForm_Resize;
            this.Load += ManagementForm_Load;

            this.ResumeLayout(false);
        }

        private void ManagementForm_Load(object? sender, EventArgs e)
        {
            // Initial layout adjustment
            AdjustLayout();
        }

        private void ManagementForm_Resize(object? sender, EventArgs e)
        {
            // Adjust layout when form is resized
            AdjustLayout();
        }

        private void AdjustLayout()
        {
            if (this.WindowState == FormWindowState.Minimized)
                return;

            // Get current form dimensions
            int formWidth = this.ClientSize.Width;
            int formHeight = this.ClientSize.Height;

            // Adjust logo position (center horizontally)
            var logoLabel = this.Controls.OfType<Label>().FirstOrDefault(l => l.Text == "LOGO");
            if (logoLabel != null)
            {
                logoLabel.Location = new Point(formWidth / 2 - 100, 20);
            }

            // Adjust button positions
            var buttons = this.Controls.OfType<Button>().ToList();
            if (buttons.Count >= 4)
            {
                int buttonSpacing = Math.Max(100, formWidth / 6);
                int startX = Math.Max(50, (formWidth - (buttons.Count * buttonSpacing)) / 2);

                for (int i = 0; i < buttons.Count; i++)
                {
                    buttons[i].Location = new Point(startX + (i * buttonSpacing), 80);
                }
            }

            // Adjust tab control size and position
            if (_mainTabControl != null)
            {
                _mainTabControl.Location = new Point(20, 130);
                _mainTabControl.Size = new Size(formWidth - 40, formHeight - 160);

                // Adjust DataGridView sizes within tabs
                foreach (TabPage tab in _mainTabControl.TabPages)
                {
                    foreach (Control control in tab.Controls)
                    {
                        if (control is DataGridView dgv)
                        {
                            dgv.Size = new Size(tab.Width - 20, tab.Height - 60);
                        }
                    }
                }
            }

            // Adjust status label position
            var statusLabel = this.Controls.Find("statusLabel", true).FirstOrDefault() as Label;
            if (statusLabel != null)
            {
                statusLabel.Location = new Point(50, formHeight - 50);
            }
        }

        private void ManagementForm_KeyDown(object? sender, KeyEventArgs e)
        {
            // Handle keyboard shortcuts
            switch (e.KeyCode)
            {
                case Keys.F11:
                    // F11 toggles fullscreen
                    ToggleFullscreen();
                    e.Handled = true;
                    break;
                case Keys.F5:
                    // F5 refreshes data
                    LoadData();
                    e.Handled = true;
                    break;
                case Keys.Escape:
                    // Escape key logs out
                    LogoutButton_Click(this, EventArgs.Empty);
                    e.Handled = true;
                    break;
            }
        }

        private void ToggleFullscreen()
        {
            if (this.WindowState == FormWindowState.Maximized && this.FormBorderStyle == FormBorderStyle.None)
            {
                // Exit fullscreen
                this.FormBorderStyle = FormBorderStyle.Sizable;
                this.WindowState = FormWindowState.Maximized;
            }
            else
            {
                // Enter fullscreen
                this.FormBorderStyle = FormBorderStyle.None;
                this.WindowState = FormWindowState.Maximized;
            }
            AdjustLayout();
        }
        
        private async void LoadData()
        {
            try
            {
                // Load all items for traveler view
                var allItems = await _itemRepository.GetAllItemsAsync();
                SetupTravelerDataGridView(allItems);
                
                // Load user's items for owner view
                var userItems = await _itemRepository.GetItemsByUserAsync(_currentUser.ID);
                SetupOwnerDataGridView(userItems);
                
                // Update status
                var statusLabel = this.Controls.Find("statusLabel", true)[0] as Label;
                statusLabel.Text = $"{allItems.Count} items found.";
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading data: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        
        private void SetupTravelerDataGridView(List<Item> items)
        {
            // Create data source with only columns shown in wireframe: Title, Capacity, Area, Type
            var travelerData = items.Select(i => new
            {
                Title = i.Title,
                Capacity = i.Capacity,
                Area = i.Area?.Name ?? "Unknown",
                Type = i.ItemType?.Name ?? "Unknown"
            }).ToList();

            _travelerDataGridView.DataSource = travelerData;

            // Customize column headers to match wireframe exactly
            if (_travelerDataGridView.Columns.Count > 0)
            {
                _travelerDataGridView.Columns["Title"].HeaderText = "Title";
                _travelerDataGridView.Columns["Capacity"].HeaderText = "Capacity";
                _travelerDataGridView.Columns["Area"].HeaderText = "Area";
                _travelerDataGridView.Columns["Type"].HeaderText = "Type";

                // Set column widths to match wireframe proportions
                _travelerDataGridView.Columns["Title"].FillWeight = 35;
                _travelerDataGridView.Columns["Capacity"].FillWeight = 15;
                _travelerDataGridView.Columns["Area"].FillWeight = 25;
                _travelerDataGridView.Columns["Type"].FillWeight = 25;
            }
        }
        
        private void SetupOwnerDataGridView(List<Item> items)
        {
            // Create data source with columns shown in wireframe: Title, Capacity, Area, Type, Edit Details
            var ownerData = items.Select(i => new
            {
                Title = i.Title,
                Capacity = i.Capacity,
                Area = i.Area?.Name ?? "Unknown",
                Type = i.ItemType?.Name ?? "Unknown",
                ItemId = i.ID // Hidden column for Edit Details functionality
            }).ToList();

            _ownerDataGridView.DataSource = ownerData;

            // Customize column headers to match wireframe exactly
            if (_ownerDataGridView.Columns.Count > 0)
            {
                _ownerDataGridView.Columns["Title"].HeaderText = "Title";
                _ownerDataGridView.Columns["Capacity"].HeaderText = "Capacity";
                _ownerDataGridView.Columns["Area"].HeaderText = "Area";
                _ownerDataGridView.Columns["Type"].HeaderText = "Type";
                _ownerDataGridView.Columns["ItemId"].Visible = false; // Hide ID column

                // Set column widths to match wireframe proportions
                _ownerDataGridView.Columns["Title"].FillWeight = 25;
                _ownerDataGridView.Columns["Capacity"].FillWeight = 15;
                _ownerDataGridView.Columns["Area"].FillWeight = 20;
                _ownerDataGridView.Columns["Type"].FillWeight = 20;
            }

            // Add Edit Details button column
            if (_ownerDataGridView.Columns["EditDetails"] == null)
            {
                var editButtonColumn = new DataGridViewButtonColumn
                {
                    Name = "EditDetails",
                    HeaderText = "Edit Details",
                    Text = "Edit Details",
                    UseColumnTextForButtonValue = true,
                    FillWeight = 20
                };
                _ownerDataGridView.Columns.Add(editButtonColumn);
            }

            // Handle Edit Details button clicks
            _ownerDataGridView.CellClick -= OwnerDataGridView_CellClick;
            _ownerDataGridView.CellClick += OwnerDataGridView_CellClick;
        }
        
        private void LogoutButton_Click(object sender, EventArgs e)
        {
            var result = MessageBox.Show("Are you sure you want to log out?", "Confirm Logout",
                MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                // End the session
                SessionService.Instance.EndSession();
                LoggingService.LogInfo($"User {_currentUser.Username} logged out");

                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
        }

        private void SearchButton_Click(object? sender, EventArgs e)
        {
            var searchForm = new SearchForm();
            searchForm.ShowDialog();
        }

        private void SwitchToTravelerView()
        {
            // Show traveler data grid, hide owner data grid
            _travelerDataGridView.Visible = true;
            _ownerDataGridView.Visible = false;

            // Update button appearance
            var travelerButton = this.Controls.Find("travelerButton", false)[0] as Button;
            var ownerButton = this.Controls.Find("ownerButton", false)[0] as Button;

            if (travelerButton != null)
            {
                travelerButton.BackColor = Color.FromArgb(41, 128, 185); // Darker blue for active
            }
            if (ownerButton != null)
            {
                ownerButton.BackColor = Color.FromArgb(46, 204, 113); // Normal green
            }

            LoadTravelerData();
        }

        private void SwitchToOwnerView()
        {
            // Show owner data grid, hide traveler data grid
            _travelerDataGridView.Visible = false;
            _ownerDataGridView.Visible = true;

            // Update button appearance
            var travelerButton = this.Controls.Find("travelerButton", false)[0] as Button;
            var ownerButton = this.Controls.Find("ownerButton", false)[0] as Button;

            if (travelerButton != null)
            {
                travelerButton.BackColor = Color.FromArgb(52, 152, 219); // Normal blue
            }
            if (ownerButton != null)
            {
                ownerButton.BackColor = Color.FromArgb(39, 174, 96); // Darker green for active
            }

            LoadOwnerData();
        }

        private void ExitApplication()
        {
            var result = MessageBox.Show("Are you sure you want to exit Seoul Stay?",
                "Confirm Exit", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        private void LoadTravelerData()
        {
            // Load data for travelers (property search/booking view)
            LoadData();
        }

        private void LoadOwnerData()
        {
            // Load data for owners/managers (property management view)
            LoadData();
        }

        private void SearchTextBox_TextChanged(object? sender, EventArgs e)
        {
            if (sender is TextBox searchBox)
            {
                string searchTerm = searchBox.Text.ToLower();

                // Filter the traveler data grid based on search term
                if (_travelerDataGridView.DataSource is List<dynamic> items)
                {
                    var filteredItems = items.Where(item =>
                        item.Title.ToString().ToLower().Contains(searchTerm) ||
                        item.Area.ToString().ToLower().Contains(searchTerm) ||
                        item.Type.ToString().ToLower().Contains(searchTerm)
                    ).ToList();

                    _travelerDataGridView.DataSource = filteredItems;

                    // Update status
                    var statusLabel = this.Controls.Find("statusLabel", true).FirstOrDefault() as Label;
                    if (statusLabel != null)
                    {
                        statusLabel.Text = $"{filteredItems.Count} items found.";
                    }
                }
            }
        }

        private void AddListingButton_Click(object? sender, EventArgs e)
        {
            // Navigate to Add Listing form (section 1.6)
            var addListingForm = new AddEditListingForm(_currentUser);
            if (addListingForm.ShowDialog() == DialogResult.OK)
            {
                // Refresh the owner data grid to show the new listing
                LoadOwnerData();
            }
        }

        private async void OwnerDataGridView_CellClick(object? sender, DataGridViewCellEventArgs e)
        {
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                var dataGridView = sender as DataGridView;
                if (dataGridView?.Columns[e.ColumnIndex].Name == "EditDetails")
                {
                    // Get the selected item ID
                    var selectedRow = dataGridView.Rows[e.RowIndex];
                    if (selectedRow.DataBoundItem != null)
                    {
                        try
                        {
                            // Extract item ID from the data bound item
                            var itemData = selectedRow.DataBoundItem;
                            var itemIdProperty = itemData.GetType().GetProperty("ItemId");
                            if (itemIdProperty != null)
                            {
                                var itemId = (long)itemIdProperty.GetValue(itemData);

                                // Load the full item for editing
                                var item = await _itemRepository.GetItemByIdAsync(itemId);
                                if (item != null)
                                {
                                    // Navigate to Edit Details form (section 1.6)
                                    var editListingForm = new AddEditListingForm(_currentUser, item);
                                    if (editListingForm.ShowDialog() == DialogResult.OK)
                                    {
                                        // Refresh the owner data grid to show updated listing
                                        LoadOwnerData();
                                    }
                                }
                                else
                                {
                                    MessageBox.Show("Item not found.", "Error",
                                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            MessageBox.Show($"Error loading item for editing: {ex.Message}", "Error",
                                MessageBoxButtons.OK, MessageBoxIcon.Error);
                        }
                    }
                }
            }
        }
    }
}
