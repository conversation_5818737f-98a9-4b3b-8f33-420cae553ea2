using System;
using System.IO;
using System.Text;

namespace SeoulStayApp.Services
{
    public enum LogLevel
    {
        Debug,
        Info,
        Warning,
        Error,
        Critical
    }
    
    public static class LoggingService
    {
        private static readonly object _lock = new object();
        private static string _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
        private static LogLevel _minimumLogLevel = LogLevel.Info;
        
        static LoggingService()
        {
            // Ensure log directory exists
            if (!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }
        }
        
        /// <summary>
        /// Sets the minimum log level
        /// </summary>
        /// <param name="level">Minimum level to log</param>
        public static void SetMinimumLogLevel(LogLevel level)
        {
            _minimumLogLevel = level;
        }
        
        /// <summary>
        /// Sets the log directory
        /// </summary>
        /// <param name="directory">Directory path for log files</param>
        public static void SetLogDirectory(string directory)
        {
            if (string.IsNullOrWhiteSpace(directory))
                throw new ArgumentException("Directory cannot be null or empty", nameof(directory));
            
            _logDirectory = directory;
            
            if (!Directory.Exists(_logDirectory))
            {
                Directory.CreateDirectory(_logDirectory);
            }
        }
        
        /// <summary>
        /// Logs a debug message
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Optional exception</param>
        public static void LogDebug(string message, Exception? exception = null)
        {
            Log(LogLevel.Debug, message, exception);
        }
        
        /// <summary>
        /// Logs an info message
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Optional exception</param>
        public static void LogInfo(string message, Exception? exception = null)
        {
            Log(LogLevel.Info, message, exception);
        }
        
        /// <summary>
        /// Logs a warning message
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Optional exception</param>
        public static void LogWarning(string message, Exception? exception = null)
        {
            Log(LogLevel.Warning, message, exception);
        }
        
        /// <summary>
        /// Logs an error message
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Optional exception</param>
        public static void LogError(string message, Exception? exception = null)
        {
            Log(LogLevel.Error, message, exception);
        }
        
        /// <summary>
        /// Logs a critical message
        /// </summary>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Optional exception</param>
        public static void LogCritical(string message, Exception? exception = null)
        {
            Log(LogLevel.Critical, message, exception);
        }
        
        /// <summary>
        /// Main logging method
        /// </summary>
        /// <param name="level">Log level</param>
        /// <param name="message">Message to log</param>
        /// <param name="exception">Optional exception</param>
        private static void Log(LogLevel level, string message, Exception? exception = null)
        {
            if (level < _minimumLogLevel)
                return;
            
            try
            {
                lock (_lock)
                {
                    var timestamp = DateTime.Now;
                    var logFileName = $"SeoulStay_{timestamp:yyyy-MM-dd}.log";
                    var logFilePath = Path.Combine(_logDirectory, logFileName);
                    
                    var logEntry = FormatLogEntry(timestamp, level, message, exception);
                    
                    // Write to file
                    File.AppendAllText(logFilePath, logEntry + Environment.NewLine, Encoding.UTF8);
                    
                    // Also write to console in debug mode
                    #if DEBUG
                    Console.WriteLine(logEntry);
                    #endif
                }
            }
            catch (Exception ex)
            {
                // If logging fails, write to event log or console as fallback
                Console.WriteLine($"Logging failed: {ex.Message}");
                Console.WriteLine($"Original message: {message}");
            }
        }
        
        /// <summary>
        /// Formats a log entry
        /// </summary>
        /// <param name="timestamp">Log timestamp</param>
        /// <param name="level">Log level</param>
        /// <param name="message">Log message</param>
        /// <param name="exception">Optional exception</param>
        /// <returns>Formatted log entry</returns>
        private static string FormatLogEntry(DateTime timestamp, LogLevel level, string message, Exception? exception)
        {
            var sb = new StringBuilder();
            
            // Timestamp and level
            sb.Append($"[{timestamp:yyyy-MM-dd HH:mm:ss.fff}] ");
            sb.Append($"[{level.ToString().ToUpper().PadRight(8)}] ");
            
            // Thread ID
            sb.Append($"[Thread-{System.Threading.Thread.CurrentThread.ManagedThreadId:D2}] ");
            
            // Message
            sb.Append(message);
            
            // Exception details if provided
            if (exception != null)
            {
                sb.AppendLine();
                sb.Append($"Exception: {exception.GetType().Name}: {exception.Message}");
                
                if (!string.IsNullOrEmpty(exception.StackTrace))
                {
                    sb.AppendLine();
                    sb.Append($"StackTrace: {exception.StackTrace}");
                }
                
                // Inner exceptions
                var innerEx = exception.InnerException;
                while (innerEx != null)
                {
                    sb.AppendLine();
                    sb.Append($"Inner Exception: {innerEx.GetType().Name}: {innerEx.Message}");
                    innerEx = innerEx.InnerException;
                }
            }
            
            return sb.ToString();
        }
        
        /// <summary>
        /// Cleans up old log files
        /// </summary>
        /// <param name="daysToKeep">Number of days to keep log files</param>
        public static void CleanupOldLogs(int daysToKeep = 30)
        {
            try
            {
                var cutoffDate = DateTime.Now.AddDays(-daysToKeep);
                var logFiles = Directory.GetFiles(_logDirectory, "SeoulStay_*.log");
                
                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                        LogInfo($"Deleted old log file: {Path.GetFileName(logFile)}");
                    }
                }
            }
            catch (Exception ex)
            {
                LogError("Failed to cleanup old log files", ex);
            }
        }
        
        /// <summary>
        /// Gets the current log file path
        /// </summary>
        /// <returns>Path to current log file</returns>
        public static string GetCurrentLogFilePath()
        {
            var logFileName = $"SeoulStay_{DateTime.Now:yyyy-MM-dd}.log";
            return Path.Combine(_logDirectory, logFileName);
        }
    }
}
