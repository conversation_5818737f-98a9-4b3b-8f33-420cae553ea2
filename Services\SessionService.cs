using SeoulStayApp.Models;
using System;
using System.Collections.Generic;

namespace SeoulStayApp.Services
{
    public class SessionService
    {
        private static SessionService? _instance;
        private static readonly object _lock = new object();
        
        private User? _currentUser;
        private string? _sessionToken;
        private DateTime _sessionExpiry;
        private readonly Dictionary<string, object> _sessionData;
        
        private SessionService()
        {
            _sessionData = new Dictionary<string, object>();
        }
        
        public static SessionService Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        _instance ??= new SessionService();
                    }
                }
                return _instance;
            }
        }
        
        /// <summary>
        /// Current logged-in user
        /// </summary>
        public User? CurrentUser => _currentUser;
        
        /// <summary>
        /// Current session token
        /// </summary>
        public string? SessionToken => _sessionToken;
        
        /// <summary>
        /// Checks if user is authenticated and session is valid
        /// </summary>
        public bool IsAuthenticated => _currentUser != null && 
                                      !string.IsNullOrEmpty(_sessionToken) && 
                                      DateTime.Now < _sessionExpiry;
        
        /// <summary>
        /// Time remaining in current session
        /// </summary>
        public TimeSpan TimeRemaining => _sessionExpiry > DateTime.Now ? 
                                        _sessionExpiry - DateTime.Now : 
                                        TimeSpan.Zero;
        
        /// <summary>
        /// Starts a new user session
        /// </summary>
        /// <param name="user">Authenticated user</param>
        /// <param name="rememberMe">Whether to extend session duration</param>
        public void StartSession(User user, bool rememberMe = false)
        {
            if (user == null)
                throw new ArgumentNullException(nameof(user));
            
            _currentUser = user;
            _sessionToken = SecurityService.GenerateSecureToken();
            
            // Set session expiry based on remember me option
            var sessionDuration = rememberMe ? TimeSpan.FromDays(30) : TimeSpan.FromHours(8);
            _sessionExpiry = DateTime.Now.Add(sessionDuration);
            
            // Clear any existing session data
            _sessionData.Clear();
            
            // Log session start
            LoggingService.LogInfo($"Session started for user: {user.Username} (ID: {user.ID})");
        }
        
        /// <summary>
        /// Ends the current session
        /// </summary>
        public void EndSession()
        {
            if (_currentUser != null)
            {
                LoggingService.LogInfo($"Session ended for user: {_currentUser.Username} (ID: {_currentUser.ID})");
            }
            
            _currentUser = null;
            _sessionToken = null;
            _sessionExpiry = DateTime.MinValue;
            _sessionData.Clear();
        }
        
        /// <summary>
        /// Extends the current session
        /// </summary>
        /// <param name="additionalTime">Additional time to add to session</param>
        public void ExtendSession(TimeSpan additionalTime)
        {
            if (IsAuthenticated)
            {
                _sessionExpiry = _sessionExpiry.Add(additionalTime);
                LoggingService.LogInfo($"Session extended for user: {_currentUser?.Username}");
            }
        }
        
        /// <summary>
        /// Refreshes the session expiry to current time + default duration
        /// </summary>
        public void RefreshSession()
        {
            if (IsAuthenticated)
            {
                _sessionExpiry = DateTime.Now.AddHours(8);
                LoggingService.LogInfo($"Session refreshed for user: {_currentUser?.Username}");
            }
        }
        
        /// <summary>
        /// Stores data in the current session
        /// </summary>
        /// <param name="key">Data key</param>
        /// <param name="value">Data value</param>
        public void SetSessionData(string key, object value)
        {
            if (string.IsNullOrWhiteSpace(key))
                throw new ArgumentException("Key cannot be null or empty", nameof(key));
            
            _sessionData[key] = value;
        }
        
        /// <summary>
        /// Retrieves data from the current session
        /// </summary>
        /// <typeparam name="T">Type of data to retrieve</typeparam>
        /// <param name="key">Data key</param>
        /// <returns>Data value or default</returns>
        public T? GetSessionData<T>(string key)
        {
            if (string.IsNullOrWhiteSpace(key) || !_sessionData.ContainsKey(key))
                return default;
            
            try
            {
                return (T)_sessionData[key];
            }
            catch
            {
                return default;
            }
        }
        
        /// <summary>
        /// Removes data from the current session
        /// </summary>
        /// <param name="key">Data key</param>
        public void RemoveSessionData(string key)
        {
            if (!string.IsNullOrWhiteSpace(key))
            {
                _sessionData.Remove(key);
            }
        }
        
        /// <summary>
        /// Checks if user has specific permission
        /// </summary>
        /// <param name="permission">Permission to check</param>
        /// <returns>True if user has permission</returns>
        public bool HasPermission(string permission)
        {
            if (!IsAuthenticated || _currentUser?.UserType == null)
                return false;
            
            // Define permissions based on user type
            var userTypeName = _currentUser.UserType.Name.ToLower();
            
            return permission.ToLower() switch
            {
                "view_all_properties" => userTypeName is "employee" or "manager",
                "manage_properties" => userTypeName is "owner" or "manager" or "employee",
                "book_properties" => userTypeName is "traveler" or "owner",
                "admin_functions" => userTypeName is "employee" or "manager",
                "view_own_properties" => userTypeName is "owner" or "manager",
                _ => false
            };
        }
    }
}
