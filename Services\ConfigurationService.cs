using System;
using System.Configuration;
using System.IO;
using System.Text.Json;

namespace SeoulStayApp.Services
{
    public class AppConfiguration
    {
        public string ConnectionString { get; set; } = "Server=localhost;Database=DataBase;Integrated Security=true;TrustServerCertificate=true;";
        public LogLevel MinimumLogLevel { get; set; } = LogLevel.Info;
        public string LogDirectory { get; set; } = "Logs";
        public int SessionTimeoutHours { get; set; } = 8;
        public int RememberMeDays { get; set; } = 30;
        public bool EnableDetailedLogging { get; set; } = true;
        public int MaxLoginAttempts { get; set; } = 5;
        public int LoginAttemptWindowMinutes { get; set; } = 15;
        public bool RequireStrongPasswords { get; set; } = true;
        public int PasswordMinLength { get; set; } = 8;
        public int PasswordMaxLength { get; set; } = 128;
        public bool AutoMigratePasswords { get; set; } = true;
        public int LogRetentionDays { get; set; } = 30;
    }
    
    public static class ConfigurationService
    {
        private static AppConfiguration? _configuration;
        private static readonly object _lock = new object();
        private static readonly string _configFilePath = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory, "appsettings.json");
        
        /// <summary>
        /// Gets the current application configuration
        /// </summary>
        public static AppConfiguration Configuration
        {
            get
            {
                if (_configuration == null)
                {
                    lock (_lock)
                    {
                        _configuration ??= LoadConfiguration();
                    }
                }
                return _configuration;
            }
        }
        
        /// <summary>
        /// Loads configuration from file or creates default
        /// </summary>
        /// <returns>Application configuration</returns>
        private static AppConfiguration LoadConfiguration()
        {
            try
            {
                if (File.Exists(_configFilePath))
                {
                    var json = File.ReadAllText(_configFilePath);
                    var config = JsonSerializer.Deserialize<AppConfiguration>(json);
                    
                    if (config != null)
                    {
                        LoggingService.LogInfo("Configuration loaded from file");
                        return config;
                    }
                }
                
                // Create default configuration
                var defaultConfig = new AppConfiguration();
                SaveConfiguration(defaultConfig);
                LoggingService.LogInfo("Default configuration created");
                return defaultConfig;
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to load configuration, using defaults", ex);
                return new AppConfiguration();
            }
        }
        
        /// <summary>
        /// Saves configuration to file
        /// </summary>
        /// <param name="configuration">Configuration to save</param>
        public static void SaveConfiguration(AppConfiguration configuration)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                
                var json = JsonSerializer.Serialize(configuration, options);
                File.WriteAllText(_configFilePath, json);
                
                _configuration = configuration;
                LoggingService.LogInfo("Configuration saved to file");
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Failed to save configuration", ex);
                throw;
            }
        }
        
        /// <summary>
        /// Reloads configuration from file
        /// </summary>
        public static void ReloadConfiguration()
        {
            lock (_lock)
            {
                _configuration = null;
                _ = Configuration; // Trigger reload
            }
        }
        
        /// <summary>
        /// Updates a specific configuration value
        /// </summary>
        /// <param name="updateAction">Action to update configuration</param>
        public static void UpdateConfiguration(Action<AppConfiguration> updateAction)
        {
            if (updateAction == null)
                throw new ArgumentNullException(nameof(updateAction));
            
            lock (_lock)
            {
                var config = Configuration;
                updateAction(config);
                SaveConfiguration(config);
            }
        }
        
        /// <summary>
        /// Gets connection string with fallback to app.config
        /// </summary>
        /// <returns>Database connection string</returns>
        public static string GetConnectionString()
        {
            try
            {
                // Try configuration file first
                var configString = Configuration.ConnectionString;
                if (!string.IsNullOrWhiteSpace(configString))
                    return configString;
                
                // Fallback to app.config
                var appConfigString = ConfigurationManager.ConnectionStrings["DefaultConnection"]?.ConnectionString;
                if (!string.IsNullOrWhiteSpace(appConfigString))
                    return appConfigString;
                
                // Final fallback to default
                return "Server=localhost;Database=DataBase;Integrated Security=true;TrustServerCertificate=true;";
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Error getting connection string, using default", ex);
                return "Server=localhost;Database=DataBase;Integrated Security=true;TrustServerCertificate=true;";
            }
        }
        
        /// <summary>
        /// Validates configuration values
        /// </summary>
        /// <returns>Validation result</returns>
        public static (bool IsValid, string[] Errors) ValidateConfiguration()
        {
            var errors = new List<string>();
            var config = Configuration;
            
            // Validate connection string
            if (string.IsNullOrWhiteSpace(config.ConnectionString))
                errors.Add("Connection string cannot be empty");
            
            // Validate session timeout
            if (config.SessionTimeoutHours < 1 || config.SessionTimeoutHours > 24)
                errors.Add("Session timeout must be between 1 and 24 hours");
            
            // Validate remember me days
            if (config.RememberMeDays < 1 || config.RememberMeDays > 365)
                errors.Add("Remember me days must be between 1 and 365");
            
            // Validate password settings
            if (config.PasswordMinLength < 4 || config.PasswordMinLength > config.PasswordMaxLength)
                errors.Add("Password minimum length must be at least 4 and less than maximum length");
            
            if (config.PasswordMaxLength < config.PasswordMinLength || config.PasswordMaxLength > 256)
                errors.Add("Password maximum length must be greater than minimum length and less than 256");
            
            // Validate login attempt settings
            if (config.MaxLoginAttempts < 1 || config.MaxLoginAttempts > 100)
                errors.Add("Max login attempts must be between 1 and 100");
            
            if (config.LoginAttemptWindowMinutes < 1 || config.LoginAttemptWindowMinutes > 1440)
                errors.Add("Login attempt window must be between 1 and 1440 minutes");
            
            // Validate log retention
            if (config.LogRetentionDays < 1 || config.LogRetentionDays > 3650)
                errors.Add("Log retention days must be between 1 and 3650");
            
            return (errors.Count == 0, errors.ToArray());
        }
        
        /// <summary>
        /// Resets configuration to defaults
        /// </summary>
        public static void ResetToDefaults()
        {
            lock (_lock)
            {
                var defaultConfig = new AppConfiguration();
                SaveConfiguration(defaultConfig);
                LoggingService.LogInfo("Configuration reset to defaults");
            }
        }
    }
}
