using SeoulStayApp.Data;
using SeoulStayApp.Models;
using SeoulStayApp.Services;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace SeoulStayApp.Forms
{
    public partial class PropertyDetailsForm : Form
    {
        private long _itemId;
        private Item? _item;
        private ItemRepository _itemRepository;
        private BookingRepository _bookingRepository;
        
        public PropertyDetailsForm(long itemId)
        {
            _itemId = itemId;
            _itemRepository = new ItemRepository();
            _bookingRepository = new BookingRepository();
            
            InitializeComponent();
            LoadPropertyDetails();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "Property Details";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            
            // Main panel with scroll
            var mainPanel = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(760, 500),
                AutoScroll = true,
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(mainPanel);
            
            // Title
            var titleLabel = new Label
            {
                Name = "titleLabel",
                Text = "Loading...",
                Location = new Point(10, 10),
                Size = new Size(720, 30),
                Font = new Font("Arial", 14, FontStyle.Bold)
            };
            mainPanel.Controls.Add(titleLabel);
            
            // Property Info Panel
            var infoPanel = new GroupBox
            {
                Text = "Property Information",
                Location = new Point(10, 50),
                Size = new Size(720, 200),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(infoPanel);
            
            // Area and Type
            var areaTypeLabel = new Label
            {
                Name = "areaTypeLabel",
                Text = "Area: - | Type: -",
                Location = new Point(10, 25),
                Size = new Size(700, 20),
                Font = new Font("Arial", 9)
            };
            infoPanel.Controls.Add(areaTypeLabel);
            
            // Capacity and Rooms
            var capacityLabel = new Label
            {
                Name = "capacityLabel",
                Text = "Capacity: - | Bedrooms: - | Bathrooms: -",
                Location = new Point(10, 50),
                Size = new Size(700, 20),
                Font = new Font("Arial", 9)
            };
            infoPanel.Controls.Add(capacityLabel);
            
            // Address
            var addressLabel = new Label
            {
                Name = "addressLabel",
                Text = "Address: -",
                Location = new Point(10, 75),
                Size = new Size(700, 20),
                Font = new Font("Arial", 9)
            };
            infoPanel.Controls.Add(addressLabel);
            
            // Owner
            var ownerLabel = new Label
            {
                Name = "ownerLabel",
                Text = "Owner: -",
                Location = new Point(10, 100),
                Size = new Size(700, 20),
                Font = new Font("Arial", 9)
            };
            infoPanel.Controls.Add(ownerLabel);
            
            // Nights
            var nightsLabel = new Label
            {
                Name = "nightsLabel",
                Text = "Minimum Nights: - | Maximum Nights: -",
                Location = new Point(10, 125),
                Size = new Size(700, 20),
                Font = new Font("Arial", 9)
            };
            infoPanel.Controls.Add(nightsLabel);
            
            // Description Panel
            var descPanel = new GroupBox
            {
                Text = "Description",
                Location = new Point(10, 260),
                Size = new Size(720, 100),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(descPanel);
            
            var descriptionTextBox = new TextBox
            {
                Name = "descriptionTextBox",
                Location = new Point(10, 25),
                Size = new Size(700, 65),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Font = new Font("Arial", 9)
            };
            descPanel.Controls.Add(descriptionTextBox);
            
            // Host Rules Panel
            var rulesPanel = new GroupBox
            {
                Text = "Host Rules",
                Location = new Point(10, 370),
                Size = new Size(720, 100),
                Font = new Font("Arial", 10, FontStyle.Bold)
            };
            mainPanel.Controls.Add(rulesPanel);
            
            var rulesTextBox = new TextBox
            {
                Name = "rulesTextBox",
                Location = new Point(10, 25),
                Size = new Size(700, 65),
                Multiline = true,
                ReadOnly = true,
                ScrollBars = ScrollBars.Vertical,
                Font = new Font("Arial", 9)
            };
            rulesPanel.Controls.Add(rulesTextBox);
            
            // Booking Panel (only for travelers)
            var bookingPanel = new GroupBox
            {
                Name = "bookingPanel",
                Text = "Make a Booking",
                Location = new Point(10, 480),
                Size = new Size(720, 120),
                Font = new Font("Arial", 10, FontStyle.Bold),
                Visible = SessionService.Instance.CurrentUser?.UserType?.Name.ToLower() == "traveler"
            };
            mainPanel.Controls.Add(bookingPanel);
            
            // Check-in Date
            var checkInLabel = new Label
            {
                Text = "Check-in:",
                Location = new Point(10, 30),
                Size = new Size(70, 23)
            };
            bookingPanel.Controls.Add(checkInLabel);
            
            var checkInDatePicker = new DateTimePicker
            {
                Name = "checkInDatePicker",
                Location = new Point(90, 30),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                MinDate = DateTime.Today
            };
            bookingPanel.Controls.Add(checkInDatePicker);
            
            // Check-out Date
            var checkOutLabel = new Label
            {
                Text = "Check-out:",
                Location = new Point(230, 30),
                Size = new Size(70, 23)
            };
            bookingPanel.Controls.Add(checkOutLabel);
            
            var checkOutDatePicker = new DateTimePicker
            {
                Name = "checkOutDatePicker",
                Location = new Point(310, 30),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                MinDate = DateTime.Today.AddDays(1)
            };
            bookingPanel.Controls.Add(checkOutDatePicker);
            
            // Guests
            var guestsLabel = new Label
            {
                Text = "Guests:",
                Location = new Point(450, 30),
                Size = new Size(50, 23)
            };
            bookingPanel.Controls.Add(guestsLabel);
            
            var guestsNumeric = new NumericUpDown
            {
                Name = "guestsNumeric",
                Location = new Point(510, 30),
                Size = new Size(60, 23),
                Minimum = 1,
                Maximum = 20,
                Value = 1
            };
            bookingPanel.Controls.Add(guestsNumeric);
            
            // Special Requests
            var requestsLabel = new Label
            {
                Text = "Special Requests:",
                Location = new Point(10, 65),
                Size = new Size(120, 23)
            };
            bookingPanel.Controls.Add(requestsLabel);
            
            var requestsTextBox = new TextBox
            {
                Name = "requestsTextBox",
                Location = new Point(140, 65),
                Size = new Size(430, 23),
                PlaceholderText = "Any special requests or notes..."
            };
            bookingPanel.Controls.Add(requestsTextBox);
            
            // Book Button
            var bookButton = new Button
            {
                Name = "bookButton",
                Text = "Book Now",
                Location = new Point(590, 65),
                Size = new Size(80, 30),
                BackColor = Color.LightGreen
            };
            bookButton.Click += BookButton_Click;
            bookingPanel.Controls.Add(bookButton);
            
            // Close Button
            var closeButton = new Button
            {
                Text = "Close",
                Location = new Point(700, 520),
                Size = new Size(80, 30)
            };
            closeButton.Click += (s, e) => this.Close();
            this.Controls.Add(closeButton);
            
            this.ResumeLayout(false);
        }
        
        private async void LoadPropertyDetails()
        {
            try
            {
                // For now, we'll get the item from the search results
                // In a full implementation, we'd have a GetItemByIdAsync method
                var allItems = await _itemRepository.GetAllItemsAsync();
                _item = allItems.Find(i => i.ID == _itemId);
                
                if (_item != null)
                {
                    UpdatePropertyDisplay();
                }
                else
                {
                    MessageBox.Show("Property not found.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    this.Close();
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error loading property details for ID {_itemId}", ex);
                MessageBox.Show("Error loading property details.", "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
                this.Close();
            }
        }
        
        private void UpdatePropertyDisplay()
        {
            if (_item == null) return;
            
            this.Text = $"Property Details - {_item.Title}";
            
            var titleLabel = this.Controls.Find("titleLabel", true)[0] as Label;
            titleLabel!.Text = _item.Title;
            
            var areaTypeLabel = this.Controls.Find("areaTypeLabel", true)[0] as Label;
            areaTypeLabel!.Text = $"Area: {_item.Area?.Name ?? "Unknown"} | Type: {_item.ItemType?.Name ?? "Unknown"}";
            
            var capacityLabel = this.Controls.Find("capacityLabel", true)[0] as Label;
            capacityLabel!.Text = $"Capacity: {_item.Capacity} | Bedrooms: {_item.NumberOfBedrooms} | Bathrooms: {_item.NumberOfBathrooms}";
            
            var addressLabel = this.Controls.Find("addressLabel", true)[0] as Label;
            addressLabel!.Text = $"Address: {_item.ApproximateAddress}";
            
            var ownerLabel = this.Controls.Find("ownerLabel", true)[0] as Label;
            ownerLabel!.Text = $"Owner: {_item.User?.FullName ?? "Unknown"}";
            
            var nightsLabel = this.Controls.Find("nightsLabel", true)[0] as Label;
            nightsLabel!.Text = $"Minimum Nights: {_item.MinimumNights} | Maximum Nights: {_item.MaximumNights}";
            
            var descriptionTextBox = this.Controls.Find("descriptionTextBox", true)[0] as TextBox;
            descriptionTextBox!.Text = _item.Description;
            
            var rulesTextBox = this.Controls.Find("rulesTextBox", true)[0] as TextBox;
            rulesTextBox!.Text = _item.HostRules;
        }
        
        private async void BookButton_Click(object? sender, EventArgs e)
        {
            if (_item == null || SessionService.Instance.CurrentUser == null) return;
            
            try
            {
                var checkInDatePicker = this.Controls.Find("checkInDatePicker", true)[0] as DateTimePicker;
                var checkOutDatePicker = this.Controls.Find("checkOutDatePicker", true)[0] as DateTimePicker;
                var guestsNumeric = this.Controls.Find("guestsNumeric", true)[0] as NumericUpDown;
                var requestsTextBox = this.Controls.Find("requestsTextBox", true)[0] as TextBox;
                
                var checkIn = checkInDatePicker!.Value.Date;
                var checkOut = checkOutDatePicker!.Value.Date;
                var guests = (int)guestsNumeric!.Value;
                
                // Validation
                if (checkOut <= checkIn)
                {
                    MessageBox.Show("Check-out date must be after check-in date.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                if (guests > _item.Capacity)
                {
                    MessageBox.Show($"Number of guests cannot exceed property capacity ({_item.Capacity}).", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                var nights = (checkOut - checkIn).Days;
                if (nights < _item.MinimumNights || nights > _item.MaximumNights)
                {
                    MessageBox.Show($"Stay duration must be between {_item.MinimumNights} and {_item.MaximumNights} nights.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                // Check availability
                var isAvailable = await _bookingRepository.IsPropertyAvailableAsync(_item.ID, checkIn, checkOut);
                if (!isAvailable)
                {
                    MessageBox.Show("Property is not available for the selected dates.", "Booking Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                // Create booking
                var booking = new Booking
                {
                    GUID = Guid.NewGuid(),
                    ItemID = _item.ID,
                    GuestUserID = SessionService.Instance.CurrentUser.ID,
                    CheckInDate = checkIn,
                    CheckOutDate = checkOut,
                    NumberOfGuests = guests,
                    SpecialRequests = string.IsNullOrWhiteSpace(requestsTextBox!.Text) ? null : requestsTextBox.Text,
                    TotalAmount = 100m * nights, // Simple pricing calculation
                    Status = BookingStatus.Pending,
                    BookingDate = DateTime.Now
                };
                
                var success = await _bookingRepository.CreateBookingAsync(booking);
                
                if (success)
                {
                    MessageBox.Show("Booking request submitted successfully! You will receive confirmation soon.", "Booking Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Failed to create booking. Please try again.", "Booking Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Error creating booking", ex);
                MessageBox.Show("Error creating booking. Please try again.", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
