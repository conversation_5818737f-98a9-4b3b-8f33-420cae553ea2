using System;
using System.ComponentModel.DataAnnotations;

namespace SeoulStayApp.Models
{
    public class Item
    {
        public long ID { get; set; }
        public Guid GUID { get; set; }
        public long UserID { get; set; }
        public long ItemTypeID { get; set; }
        public long AreaID { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Title { get; set; } = string.Empty;

        public int Capacity { get; set; }
        public int NumberOfBeds { get; set; }
        public int NumberOfBedrooms { get; set; }
        public int NumberOfBathrooms { get; set; }

        [Required]
        [StringLength(500)]
        public string ExactAddress { get; set; } = string.Empty;

        [Required]
        [StringLength(250)]
        public string ApproximateAddress { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string Description { get; set; } = string.Empty;

        [Required]
        [StringLength(2000)]
        public string HostRules { get; set; } = string.Empty;
        
        public int MinimumNights { get; set; }
        public int MaximumNights { get; set; }
        
        // Navigation properties
        public User? User { get; set; }
        public ItemType? ItemType { get; set; }
        public Area? Area { get; set; }
    }
}
