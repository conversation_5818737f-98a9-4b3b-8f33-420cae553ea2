using FontAwesome.Sharp;
using SeoulStayApp.Data;
using SeoulStayApp.Models;
using SeoulStayApp.Services;
using System;
using System.ComponentModel;
using System.Drawing;
using System.Windows.Forms;

namespace SeoulStayApp.Forms
{
    public partial class NewLoginForm : Form
    {
        private UserRepository _userRepository;
        
        [Browsable(false)]
        [DesignerSerializationVisibility(DesignerSerializationVisibility.Hidden)]
        public User? LoggedInUser { get; private set; }
        
        public NewLoginForm()
        {
            InitializeComponent();
            _userRepository = new UserRepository();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "Seoul Stay - Welcome";
            this.Size = new Size(700, 550);
            this.MinimumSize = new Size(650, 500); // Increased minimum size for better layout
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.BackColor = Color.FromArgb(245, 248, 250);
            this.WindowState = FormWindowState.Normal;
            this.KeyPreview = true;
            this.KeyDown += NewLoginForm_KeyDown;

            // Header Panel - matches registration form
            var headerPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(700, 80),
                BackColor = Color.FromArgb(52, 73, 94)
            };
            this.Controls.Add(headerPanel);

            // Logo Icon in header
            var logoIcon = new IconButton
            {
                IconChar = IconChar.SignInAlt,
                IconColor = Color.White,
                IconSize = 32,
                Location = new Point(20, 20),
                Size = new Size(50, 40),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent
            };
            logoIcon.FlatAppearance.BorderSize = 0;
            headerPanel.Controls.Add(logoIcon);

            // Logo Text in header
            var logoLabel = new Label
            {
                Text = "SEOUL STAY",
                Location = new Point(80, 25),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent
            };
            headerPanel.Controls.Add(logoLabel);

            // Subtitle in header
            var subtitleLabel = new Label
            {
                Text = "Welcome Back",
                Location = new Point(300, 30),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Italic),
                ForeColor = Color.FromArgb(189, 195, 199),
                BackColor = Color.Transparent
            };
            headerPanel.Controls.Add(subtitleLabel);
            
            // Main Login Panel - matches registration form style
            var loginPanel = new Panel
            {
                Name = "loginPanel",
                Location = new Point(100, 120),
                Size = new Size(500, 350),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                Anchor = AnchorStyles.None
            };
            this.Controls.Add(loginPanel);

            // Login Title - matches registration form
            var loginTitle = new Label
            {
                Text = "Sign In to Your Account",
                Location = new Point(20, 20),
                Size = new Size(460, 30),
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.MiddleCenter
            };
            loginPanel.Controls.Add(loginTitle);

            // Role Selection Section
            var roleLabel = new Label
            {
                Text = "I am a:",
                Location = new Point(30, 70),
                Size = new Size(60, 23),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            loginPanel.Controls.Add(roleLabel);

            var employeeRoleRadio = new RadioButton
            {
                Name = "employeeRoleRadio",
                Text = "Employee/Manager",
                Location = new Point(100, 70),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 9),
                Checked = true,
                TabIndex = 1
            };
            loginPanel.Controls.Add(employeeRoleRadio);

            var userRoleRadio = new RadioButton
            {
                Name = "userRoleRadio",
                Text = "Traveler/Guest",
                Location = new Point(270, 70),
                Size = new Size(130, 23),
                Font = new Font("Segoe UI", 9),
                TabIndex = 2
            };
            loginPanel.Controls.Add(userRoleRadio);

            // Username Section with Icon
            var usernameIcon = new IconPictureBox
            {
                IconChar = IconChar.User,
                IconColor = Color.FromArgb(52, 73, 94),
                IconSize = 20,
                Location = new Point(30, 110),
                Size = new Size(25, 25)
            };
            loginPanel.Controls.Add(usernameIcon);

            var usernameLabel = new Label
            {
                Text = "Username:",
                Location = new Point(65, 110),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            loginPanel.Controls.Add(usernameLabel);

            var usernameTextBox = new TextBox
            {
                Name = "usernameTextBox",
                Location = new Point(30, 135),
                Size = new Size(200, 25),
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.FixedSingle,
                TabIndex = 3
            };
            loginPanel.Controls.Add(usernameTextBox);

            // Password Section with Icon
            var passwordIcon = new IconPictureBox
            {
                IconChar = IconChar.Lock,
                IconColor = Color.FromArgb(52, 73, 94),
                IconSize = 20,
                Location = new Point(270, 110),
                Size = new Size(25, 25)
            };
            loginPanel.Controls.Add(passwordIcon);

            var passwordLabel = new Label
            {
                Text = "Password:",
                Location = new Point(305, 110),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            loginPanel.Controls.Add(passwordLabel);

            var passwordTextBox = new TextBox
            {
                Name = "passwordTextBox",
                Location = new Point(270, 135),
                Size = new Size(200, 25),
                UseSystemPasswordChar = true,
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.FixedSingle,
                TabIndex = 4
            };
            loginPanel.Controls.Add(passwordTextBox);

            // Keep me signed in CheckBox
            var keepSignedInCheckBox = new CheckBox
            {
                Name = "keepSignedInCheckBox",
                Text = "Keep me signed in",
                Location = new Point(30, 180),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 9),
                TabIndex = 5
            };
            loginPanel.Controls.Add(keepSignedInCheckBox);

            // Show Password CheckBox
            var showPasswordCheckBox = new CheckBox
            {
                Name = "showPasswordCheckBox",
                Text = "Show Password",
                Location = new Point(270, 180),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 9),
                TabIndex = 6
            };
            showPasswordCheckBox.CheckedChanged += (s, e) =>
            {
                passwordTextBox.UseSystemPasswordChar = !showPasswordCheckBox.Checked;
            };
            loginPanel.Controls.Add(showPasswordCheckBox);

            // Login Button - matches registration form style
            var loginButton = new IconButton
            {
                Name = "loginButton",
                Text = "  Sign In",
                IconChar = IconChar.SignInAlt,
                IconColor = Color.White,
                IconSize = 20,
                Location = new Point(30, 220),
                Size = new Size(180, 40),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(52, 152, 219),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                ImageAlign = ContentAlignment.MiddleLeft,
                TextAlign = ContentAlignment.MiddleCenter,
                TabIndex = 6
            };
            loginButton.FlatAppearance.BorderSize = 0;
            loginButton.Click += LoginButton_Click;

            // Add hover effects
            loginButton.MouseEnter += (s, e) => {
                loginButton.BackColor = Color.FromArgb(41, 128, 185);
                this.Cursor = Cursors.Hand;
            };
            loginButton.MouseLeave += (s, e) => {
                loginButton.BackColor = Color.FromArgb(52, 152, 219);
                this.Cursor = Cursors.Default;
            };

            loginPanel.Controls.Add(loginButton);

            // Exit Button - matches registration form style
            var exitButton = new IconButton
            {
                Name = "exitButton",
                Text = "  Exit",
                IconChar = IconChar.Times,
                IconColor = Color.White,
                IconSize = 20,
                Location = new Point(250, 220),
                Size = new Size(180, 40),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(231, 76, 60),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                ImageAlign = ContentAlignment.MiddleLeft,
                TextAlign = ContentAlignment.MiddleCenter
            };
            exitButton.FlatAppearance.BorderSize = 0;
            exitButton.Click += ExitButton_Click;

            // Add hover effects
            exitButton.MouseEnter += (s, e) => {
                exitButton.BackColor = Color.FromArgb(192, 57, 43);
                this.Cursor = Cursors.Hand;
            };
            exitButton.MouseLeave += (s, e) => {
                exitButton.BackColor = Color.FromArgb(231, 76, 60);
                this.Cursor = Cursors.Default;
            };

            loginPanel.Controls.Add(exitButton);

            // Create Account Link
            var createAccountLink = new LinkLabel
            {
                Name = "createAccountLink",
                Text = "Don't have an account? Create one",
                Location = new Point(150, 280),
                Size = new Size(200, 20),
                Font = new Font("Segoe UI", 10),
                TextAlign = ContentAlignment.MiddleCenter,
                LinkColor = Color.FromArgb(52, 152, 219)
            };
            createAccountLink.LinkClicked += CreateAccountLink_LinkClicked;
            loginPanel.Controls.Add(createAccountLink);

            // Status Bar
            var statusStrip = new StatusStrip
            {
                Name = "statusStrip",
                BackColor = Color.FromArgb(52, 73, 94),
                ForeColor = Color.White
            };

            var statusLabel = new ToolStripStatusLabel
            {
                Name = "statusLabel",
                Text = "Ready - Enter credentials to login | F11: Fullscreen | Enter: Login | Esc: Exit",
                ForeColor = Color.White,
                Spring = true
            };
            statusStrip.Items.Add(statusLabel);
            this.Controls.Add(statusStrip);

            // Add resize event handler for responsive design
            this.Resize += NewLoginForm_Resize;
            this.Load += NewLoginForm_Load;

            this.ResumeLayout(false);
        }

        private void NewLoginForm_Load(object? sender, EventArgs e)
        {
            // Initial layout adjustment
            AdjustLayout();
        }

        private void NewLoginForm_Resize(object? sender, EventArgs e)
        {
            // Adjust layout when form is resized
            AdjustLayout();
        }

        private void AdjustLayout()
        {
            if (this.WindowState == FormWindowState.Minimized)
                return;

            // Suspend layout to prevent flickering during resize
            this.SuspendLayout();

            try
            {
                // Get current form dimensions
                int formWidth = this.ClientSize.Width;
                int formHeight = this.ClientSize.Height;

                // Ensure minimum usable dimensions
                if (formWidth < 500 || formHeight < 400)
                    return;

                // Calculate responsive positions
                int centerX = formWidth / 2;

                // Adjust header panel width
                var headerPanel = this.Controls.OfType<Panel>().FirstOrDefault(p => p.BackColor == Color.FromArgb(52, 73, 94));
                if (headerPanel != null)
                {
                    headerPanel.Size = new Size(formWidth, 80);
                }

                // Main login panel - centered with proper spacing
                var loginPanel = this.Controls.Find("loginPanel", false).FirstOrDefault() as Panel;
                if (loginPanel != null)
                {
                    int panelWidth = Math.Min(500, formWidth - 100);
                    int panelHeight = 350; // Fixed height for consistency

                    int panelY = 100; // Fixed distance from top

                    loginPanel.Size = new Size(panelWidth, panelHeight);
                    loginPanel.Location = new Point(centerX - panelWidth / 2, panelY);
                }
            }
            finally
            {
                // Resume layout to apply changes
                this.ResumeLayout(true);
            }
        }

        private void NewLoginForm_KeyDown(object? sender, KeyEventArgs e)
        {
            // Handle keyboard shortcuts
            switch (e.KeyCode)
            {
                case Keys.Enter:
                    // Enter key triggers login
                    LoginButton_Click(null, EventArgs.Empty);
                    e.Handled = true;
                    break;
                case Keys.Escape:
                    // Escape key exits application
                    ExitButton_Click(null, EventArgs.Empty);
                    e.Handled = true;
                    break;
                case Keys.F11:
                    // F11 toggles fullscreen
                    ToggleFullscreen();
                    e.Handled = true;
                    break;
            }
        }

        private void ToggleFullscreen()
        {
            if (this.WindowState == FormWindowState.Maximized && this.FormBorderStyle == FormBorderStyle.None)
            {
                // Exit fullscreen
                this.FormBorderStyle = FormBorderStyle.Sizable;
                this.WindowState = FormWindowState.Normal;
            }
            else
            {
                // Enter fullscreen
                this.FormBorderStyle = FormBorderStyle.None;
                this.WindowState = FormWindowState.Maximized;
            }
            AdjustLayout();
        }

        private async void LoginButton_Click(object? sender, EventArgs e)
        {
            UpdateStatus("Validating credentials...");

            var usernameTextBox = this.Controls.Find("usernameTextBox", true)[0] as TextBox;
            var passwordTextBox = this.Controls.Find("passwordTextBox", true)[0] as TextBox;
            var employeeRoleRadio = this.Controls.Find("employeeRoleRadio", true)[0] as RadioButton;
            var userRoleRadio = this.Controls.Find("userRoleRadio", true)[0] as RadioButton;
            var keepSignedInCheckBox = this.Controls.Find("keepSignedInCheckBox", true)[0] as CheckBox;

            // Validate input fields
            if (string.IsNullOrWhiteSpace(usernameTextBox!.Text))
            {
                UpdateStatus("Username required");
                MessageBox.Show("Please enter your username.", "Login Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (string.IsNullOrWhiteSpace(passwordTextBox!.Text))
            {
                UpdateStatus("Password required");
                MessageBox.Show("Please enter your password.", "Login Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Determine expected user type based on role selection
            int expectedUserTypeId = employeeRoleRadio!.Checked ? 1 : 2; // 1 = employee, 2 = user
            string expectedRoleName = employeeRoleRadio.Checked ? "Employee/Manager" : "Traveler/Guest";

            UpdateStatus($"Authenticating {expectedRoleName} account...");

            try
            {
                // Authenticate user
                var user = await _userRepository.AuthenticateUserAsync(usernameTextBox.Text, passwordTextBox.Text);

                if (user != null)
                {
                    // Check if user's role matches selected role
                    if (user.UserTypeID != expectedUserTypeId)
                    {
                        string actualRole = user.UserTypeID == 1 ? "Employee/Manager" : "Traveler/Guest";
                        MessageBox.Show($"Access denied. You selected '{expectedRoleName}' but your account is registered as '{actualRole}'. Please select the correct role.",
                            "Role Mismatch", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Create session
                    bool rememberMe = keepSignedInCheckBox!.Checked;
                    SessionService.Instance.StartSession(user, rememberMe);

                    LoggingService.LogInfo($"User {user.Username} logged in successfully as {expectedRoleName}");

                    UpdateStatus($"Login successful! Welcome {user.FullName}");
                    this.LoggedInUser = user;
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    UpdateStatus("Login failed - Invalid credentials");
                    MessageBox.Show("Invalid username or password.", "Login Failed",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                UpdateStatus("Login error occurred");
                LoggingService.LogError("Login error", ex);
                MessageBox.Show("An error occurred during login. Please try again.", "Login Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                // Reset status after a delay if still on login form
                await Task.Delay(3000);
                if (this.Visible)
                {
                    UpdateStatus("Ready - Enter credentials to login | F11: Fullscreen | Enter: Login | Esc: Exit");
                }
            }
        }

        private void CreateAccountLink_LinkClicked(object? sender, LinkLabelLinkClickedEventArgs e)
        {
            var createAccountForm = new NewCreateAccountForm();
            if (createAccountForm.ShowDialog() == DialogResult.OK)
            {
                MessageBox.Show("Account created successfully! You can now log in.", "Registration Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void UpdateStatus(string message)
        {
            var statusStrip = this.Controls.Find("statusStrip", false).FirstOrDefault() as StatusStrip;
            if (statusStrip != null)
            {
                var statusLabel = statusStrip.Items["statusLabel"] as ToolStripStatusLabel;
                if (statusLabel != null)
                {
                    statusLabel.Text = message;
                }
            }
        }

        private void ExitButton_Click(object? sender, EventArgs e)
        {
            try
            {
                // Confirm exit
                var result = MessageBox.Show("Are you sure you want to exit Seoul Stay?",
                    "Confirm Exit", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    // Close the form properly
                    this.Close();

                    // If this is the main form, exit the application
                    Application.Exit();
                }
            }
            catch (Exception ex)
            {
                // Force exit if there's any issue
                Environment.Exit(0);
            }
        }
    }
}
