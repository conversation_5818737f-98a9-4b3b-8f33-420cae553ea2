using System;
using System.Collections.Generic;

namespace SeoulStayApp.Models
{
    public class SearchCriteria
    {
        public string? SearchText { get; set; }
        public long? AreaID { get; set; }
        public long? ItemTypeID { get; set; }
        public int? MinCapacity { get; set; }
        public int? MaxCapacity { get; set; }
        public int? MinBedrooms { get; set; }
        public int? MaxBedrooms { get; set; }
        public int? MinBathrooms { get; set; }
        public int? MaxBathrooms { get; set; }
        public decimal? MinPrice { get; set; }
        public decimal? MaxPrice { get; set; }
        public DateTime? CheckInDate { get; set; }
        public DateTime? CheckOutDate { get; set; }
        public List<long>? AmenityIDs { get; set; }
        public string? SortBy { get; set; } = "Title"; // Title, Price, Capacity, etc.
        public bool SortDescending { get; set; } = false;
        public int PageNumber { get; set; } = 1;
        public int PageSize { get; set; } = 20;
        
        public SearchCriteria()
        {
            AmenityIDs = new List<long>();
        }
    }
    
    public class SearchResult<T>
    {
        public List<T> Items { get; set; } = new List<T>();
        public int TotalCount { get; set; }
        public int PageNumber { get; set; }
        public int PageSize { get; set; }
        public int TotalPages => (int)Math.Ceiling((double)TotalCount / PageSize);
        public bool HasPreviousPage => PageNumber > 1;
        public bool HasNextPage => PageNumber < TotalPages;
    }
}
