using Microsoft.Data.SqlClient;
using SeoulStayApp.Models;
using SeoulStayApp.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SeoulStayApp.Data
{
    public class UserRepository
    {
        public async Task<User?> AuthenticateUserAsync(string username, string password)
        {
            try
            {
                LoggingService.LogInfo($"Authentication attempt for username: {username}");

                // Sanitize inputs
                username = SecurityService.SanitizeInput(username);

                // First get user with hashed password
                const string query = @"
                    SELECT u.ID, u.GUID, u.UserTypeID, u.Username, u.Password, u.FullName, u.Gender, u.BirthDate, u.FamilyCount,
                           ut.Name as UserTypeName
                    FROM Users u
                    INNER JOIN UserTypes ut ON u.UserTypeID = ut.ID
                    WHERE u.Username = @Username";

                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();
                using var command = new SqlCommand(query, connection);
                command.Parameters.Add("@Username", System.Data.SqlDbType.VarChar, 50).Value = username;

                using var reader = await command.ExecuteReaderAsync();
                if (await reader.ReadAsync())
                {
                    var storedPassword = reader.GetString(4); // Password column

                    // Verify password using BCrypt (for new hashed passwords) or plain text (for legacy)
                    bool isValidPassword = false;
                    if (storedPassword.StartsWith("$2"))
                    {
                        // BCrypt hashed password
                        isValidPassword = SecurityService.VerifyPassword(password, storedPassword);
                    }
                    else
                    {
                        // Legacy plain text password (should be migrated)
                        isValidPassword = storedPassword == password;

                        // Auto-migrate to hashed password
                        if (isValidPassword)
                        {
                            _ = Task.Run(async () => await MigratePasswordToHashAsync(reader.GetInt64(0), password));
                        }
                    }

                    if (isValidPassword)
                    {
                        LoggingService.LogInfo($"Successful authentication for user: {username}");
                        return new User
                        {
                            ID = reader.GetInt64(0),
                            GUID = reader.GetGuid(1),
                            UserTypeID = reader.GetInt64(2),
                            Username = reader.GetString(3),
                            FullName = reader.GetString(5),
                            Gender = reader.GetBoolean(6),
                            BirthDate = reader.GetDateTime(7),
                            FamilyCount = reader.GetInt32(8),
                            UserType = new UserType
                            {
                                ID = reader.GetInt64(2),
                                Name = reader.GetString(9)
                            }
                        };
                    }
                }

                LoggingService.LogWarning($"Failed authentication attempt for username: {username}");
                return null;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error during authentication for username: {username}", ex);
                throw;
            }
        }
        
        public async Task<bool> CreateUserAsync(User user, string plainPassword)
        {
            try
            {
                LoggingService.LogInfo($"Creating new user: {user.Username}");

                // Validate inputs
                var (isValidUsername, usernameMessage) = SecurityService.ValidateUsername(user.Username);
                if (!isValidUsername)
                    throw new ArgumentException(usernameMessage);

                var (isValidPassword, passwordMessage) = SecurityService.ValidatePasswordStrength(plainPassword);
                if (!isValidPassword)
                    throw new ArgumentException(passwordMessage);

                // Hash the password
                var hashedPassword = SecurityService.HashPassword(plainPassword);

                const string query = @"
                    INSERT INTO Users (GUID, UserTypeID, Username, Password, FullName, Gender, BirthDate, FamilyCount)
                    VALUES (@GUID, @UserTypeID, @Username, @Password, @FullName, @Gender, @BirthDate, @FamilyCount)";

                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();
                using var command = new SqlCommand(query, connection);

                // Use proper parameter types to prevent SQL injection
                command.Parameters.Add("@GUID", System.Data.SqlDbType.UniqueIdentifier).Value = user.GUID;
                command.Parameters.Add("@UserTypeID", System.Data.SqlDbType.BigInt).Value = user.UserTypeID;
                command.Parameters.Add("@Username", System.Data.SqlDbType.VarChar, 50).Value = SecurityService.SanitizeInput(user.Username);
                command.Parameters.Add("@Password", System.Data.SqlDbType.VarChar, 255).Value = hashedPassword;
                command.Parameters.Add("@FullName", System.Data.SqlDbType.NVarChar, 50).Value = user.FullName;
                command.Parameters.Add("@Gender", System.Data.SqlDbType.Bit).Value = user.Gender;
                command.Parameters.Add("@BirthDate", System.Data.SqlDbType.Date).Value = user.BirthDate;
                command.Parameters.Add("@FamilyCount", System.Data.SqlDbType.Int).Value = user.FamilyCount;

                var result = await command.ExecuteNonQueryAsync();

                if (result > 0)
                {
                    LoggingService.LogInfo($"Successfully created user: {user.Username}");
                    return true;
                }

                LoggingService.LogWarning($"Failed to create user: {user.Username}");
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating user: {user.Username}", ex);
                throw;
            }
        }
        
        public async Task<bool> UsernameExistsAsync(string username)
        {
            const string query = "SELECT COUNT(*) FROM Users WHERE Username = @Username";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@Username", username);
                    var count = (int)await command.ExecuteScalarAsync();
                    return count > 0;
                }
            }
        }
        
        public async Task<List<UserType>> GetUserTypesAsync()
        {
            const string query = "SELECT ID, GUID, Name FROM UserTypes";
            var userTypes = new List<UserType>();
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            userTypes.Add(new UserType
                            {
                                ID = reader.GetInt64(0),
                                GUID = reader.GetGuid(1),
                                Name = reader.GetString(2)
                            });
                        }
                    }
                }
            }
            return userTypes;
        }

        /// <summary>
        /// Migrates a plain text password to hashed password
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="plainPassword">Plain text password</param>
        private async Task MigratePasswordToHashAsync(long userId, string plainPassword)
        {
            try
            {
                var hashedPassword = SecurityService.HashPassword(plainPassword);

                const string query = "UPDATE Users SET Password = @Password WHERE ID = @UserId";

                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();
                using var command = new SqlCommand(query, connection);

                command.Parameters.Add("@Password", System.Data.SqlDbType.VarChar, 255).Value = hashedPassword;
                command.Parameters.Add("@UserId", System.Data.SqlDbType.BigInt).Value = userId;

                await command.ExecuteNonQueryAsync();

                LoggingService.LogInfo($"Migrated password to hash for user ID: {userId}");
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Failed to migrate password for user ID: {userId}", ex);
            }
        }
    }
}
