using SeoulStayApp.Data;
using SeoulStayApp.Models;
using SeoulStayApp.Services;
using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;

namespace SeoulStayApp.Forms
{
    public partial class SearchForm : Form
    {
        private ItemRepository _itemRepository;
        private SearchCriteria _currentCriteria;
        private SearchResult<Item> _currentResults;
        
        // Controls
        private TextBox _searchTextBox = null!;
        private ComboBox _areaComboBox = null!;
        private ComboBox _itemTypeComboBox = null!;
        private NumericUpDown _minCapacityNumeric = null!;
        private NumericUpDown _maxCapacityNumeric = null!;
        private NumericUpDown _minBedroomsNumeric = null!;
        private NumericUpDown _maxBedroomsNumeric = null!;
        private DateTimePicker _checkInDatePicker = null!;
        private DateTimePicker _checkOutDatePicker = null!;
        private ComboBox _sortByComboBox = null!;
        private CheckBox _sortDescendingCheckBox = null!;
        private DataGridView _resultsDataGridView = null!;
        private Label _resultsCountLabel = null!;
        private Button _previousPageButton = null!;
        private Button _nextPageButton = null!;
        private Label _pageInfoLabel = null!;
        
        public SearchForm()
        {
            _itemRepository = new ItemRepository();
            _currentCriteria = new SearchCriteria();
            _currentResults = new SearchResult<Item>();
            
            InitializeComponent();
            LoadComboBoxData();
            PerformSearch();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "Seoul Stay - Property Search";
            this.Size = new Size(1200, 800);
            this.MinimumSize = new Size(900, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.WindowState = FormWindowState.Maximized;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            
            // Search Panel
            var searchPanel = new Panel
            {
                Location = new Point(10, 10),
                Size = new Size(1160, 150),
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(searchPanel);
            
            // Search Text
            var searchLabel = new Label
            {
                Text = "Search:",
                Location = new Point(10, 15),
                Size = new Size(60, 23)
            };
            searchPanel.Controls.Add(searchLabel);
            
            _searchTextBox = new TextBox
            {
                Location = new Point(80, 15),
                Size = new Size(200, 23),
                PlaceholderText = "Title, description, or address..."
            };
            searchPanel.Controls.Add(_searchTextBox);
            
            // Area
            var areaLabel = new Label
            {
                Text = "Area:",
                Location = new Point(300, 15),
                Size = new Size(50, 23)
            };
            searchPanel.Controls.Add(areaLabel);
            
            _areaComboBox = new ComboBox
            {
                Location = new Point(360, 15),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            searchPanel.Controls.Add(_areaComboBox);
            
            // Item Type
            var typeLabel = new Label
            {
                Text = "Type:",
                Location = new Point(500, 15),
                Size = new Size(50, 23)
            };
            searchPanel.Controls.Add(typeLabel);
            
            _itemTypeComboBox = new ComboBox
            {
                Location = new Point(560, 15),
                Size = new Size(120, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            searchPanel.Controls.Add(_itemTypeComboBox);
            
            // Capacity
            var capacityLabel = new Label
            {
                Text = "Capacity:",
                Location = new Point(10, 50),
                Size = new Size(60, 23)
            };
            searchPanel.Controls.Add(capacityLabel);
            
            _minCapacityNumeric = new NumericUpDown
            {
                Location = new Point(80, 50),
                Size = new Size(60, 23),
                Minimum = 1,
                Maximum = 20
            };
            searchPanel.Controls.Add(_minCapacityNumeric);
            
            var toLabel1 = new Label
            {
                Text = "to",
                Location = new Point(150, 50),
                Size = new Size(20, 23)
            };
            searchPanel.Controls.Add(toLabel1);
            
            _maxCapacityNumeric = new NumericUpDown
            {
                Location = new Point(180, 50),
                Size = new Size(60, 23),
                Minimum = 1,
                Maximum = 20
            };
            searchPanel.Controls.Add(_maxCapacityNumeric);
            
            // Bedrooms
            var bedroomsLabel = new Label
            {
                Text = "Bedrooms:",
                Location = new Point(260, 50),
                Size = new Size(70, 23)
            };
            searchPanel.Controls.Add(bedroomsLabel);
            
            _minBedroomsNumeric = new NumericUpDown
            {
                Location = new Point(340, 50),
                Size = new Size(60, 23),
                Minimum = 0,
                Maximum = 10
            };
            searchPanel.Controls.Add(_minBedroomsNumeric);
            
            var toLabel2 = new Label
            {
                Text = "to",
                Location = new Point(410, 50),
                Size = new Size(20, 23)
            };
            searchPanel.Controls.Add(toLabel2);
            
            _maxBedroomsNumeric = new NumericUpDown
            {
                Location = new Point(440, 50),
                Size = new Size(60, 23),
                Minimum = 0,
                Maximum = 10
            };
            searchPanel.Controls.Add(_maxBedroomsNumeric);
            
            // Check-in Date
            var checkInLabel = new Label
            {
                Text = "Check-in:",
                Location = new Point(10, 85),
                Size = new Size(70, 23)
            };
            searchPanel.Controls.Add(checkInLabel);
            
            _checkInDatePicker = new DateTimePicker
            {
                Location = new Point(90, 85),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                MinDate = DateTime.Today
            };
            searchPanel.Controls.Add(_checkInDatePicker);
            
            // Check-out Date
            var checkOutLabel = new Label
            {
                Text = "Check-out:",
                Location = new Point(230, 85),
                Size = new Size(70, 23)
            };
            searchPanel.Controls.Add(checkOutLabel);
            
            _checkOutDatePicker = new DateTimePicker
            {
                Location = new Point(310, 85),
                Size = new Size(120, 23),
                Format = DateTimePickerFormat.Short,
                MinDate = DateTime.Today.AddDays(1)
            };
            searchPanel.Controls.Add(_checkOutDatePicker);
            
            // Sort By
            var sortLabel = new Label
            {
                Text = "Sort by:",
                Location = new Point(450, 85),
                Size = new Size(60, 23)
            };
            searchPanel.Controls.Add(sortLabel);
            
            _sortByComboBox = new ComboBox
            {
                Location = new Point(520, 85),
                Size = new Size(100, 23),
                DropDownStyle = ComboBoxStyle.DropDownList
            };
            _sortByComboBox.Items.AddRange(new[] { "Title", "Area", "Type", "Capacity", "Bedrooms" });
            _sortByComboBox.SelectedIndex = 0;
            searchPanel.Controls.Add(_sortByComboBox);
            
            _sortDescendingCheckBox = new CheckBox
            {
                Text = "Descending",
                Location = new Point(630, 85),
                Size = new Size(90, 23)
            };
            searchPanel.Controls.Add(_sortDescendingCheckBox);
            
            // Search Button
            var searchButton = new Button
            {
                Text = "Search",
                Location = new Point(750, 50),
                Size = new Size(80, 30),
                BackColor = Color.LightBlue
            };
            searchButton.Click += SearchButton_Click;
            searchPanel.Controls.Add(searchButton);
            
            // Clear Button
            var clearButton = new Button
            {
                Text = "Clear",
                Location = new Point(840, 50),
                Size = new Size(80, 30)
            };
            clearButton.Click += ClearButton_Click;
            searchPanel.Controls.Add(clearButton);
            
            // Results Panel
            var resultsPanel = new Panel
            {
                Location = new Point(10, 170),
                Size = new Size(1160, 500),
                BorderStyle = BorderStyle.FixedSingle
            };
            this.Controls.Add(resultsPanel);
            
            // Results Count
            _resultsCountLabel = new Label
            {
                Text = "0 properties found",
                Location = new Point(10, 10),
                Size = new Size(200, 23),
                Font = new Font("Arial", 9, FontStyle.Bold)
            };
            resultsPanel.Controls.Add(_resultsCountLabel);
            
            // Results DataGridView
            _resultsDataGridView = new DataGridView
            {
                Location = new Point(10, 40),
                Size = new Size(1140, 420),
                AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill,
                ReadOnly = true,
                AllowUserToAddRows = false,
                SelectionMode = DataGridViewSelectionMode.FullRowSelect
            };
            _resultsDataGridView.DoubleClick += ResultsDataGridView_DoubleClick;
            resultsPanel.Controls.Add(_resultsDataGridView);
            
            // Pagination Panel
            var paginationPanel = new Panel
            {
                Location = new Point(10, 470),
                Size = new Size(1140, 40)
            };
            resultsPanel.Controls.Add(paginationPanel);
            
            _previousPageButton = new Button
            {
                Text = "< Previous",
                Location = new Point(400, 5),
                Size = new Size(80, 30),
                Enabled = false
            };
            _previousPageButton.Click += PreviousPageButton_Click;
            paginationPanel.Controls.Add(_previousPageButton);
            
            _pageInfoLabel = new Label
            {
                Text = "Page 1 of 1",
                Location = new Point(490, 10),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleCenter
            };
            paginationPanel.Controls.Add(_pageInfoLabel);
            
            _nextPageButton = new Button
            {
                Text = "Next >",
                Location = new Point(600, 5),
                Size = new Size(80, 30),
                Enabled = false
            };
            _nextPageButton.Click += NextPageButton_Click;
            paginationPanel.Controls.Add(_nextPageButton);
            
            // Close Button
            var closeButton = new Button
            {
                Text = "Close",
                Location = new Point(1090, 680),
                Size = new Size(80, 30)
            };
            closeButton.Click += (s, e) => this.Close();
            this.Controls.Add(closeButton);
            
            this.ResumeLayout(false);
        }

        private async void LoadComboBoxData()
        {
            try
            {
                // Load areas
                var areas = await _itemRepository.GetAreasAsync();
                _areaComboBox.Items.Add(new { ID = (long?)null, Name = "All Areas" });
                foreach (var area in areas)
                {
                    _areaComboBox.Items.Add(new { ID = (long?)area.ID, Name = area.Name });
                }
                _areaComboBox.DisplayMember = "Name";
                _areaComboBox.ValueMember = "ID";
                _areaComboBox.SelectedIndex = 0;

                // Load item types
                var itemTypes = await _itemRepository.GetItemTypesAsync();
                _itemTypeComboBox.Items.Add(new { ID = (long?)null, Name = "All Types" });
                foreach (var itemType in itemTypes)
                {
                    _itemTypeComboBox.Items.Add(new { ID = (long?)itemType.ID, Name = itemType.Name });
                }
                _itemTypeComboBox.DisplayMember = "Name";
                _itemTypeComboBox.ValueMember = "ID";
                _itemTypeComboBox.SelectedIndex = 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Error loading combo box data", ex);
                MessageBox.Show("Error loading search options. Please try again.", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private async void SearchButton_Click(object? sender, EventArgs e)
        {
            await PerformSearch();
        }

        private void ClearButton_Click(object? sender, EventArgs e)
        {
            _searchTextBox.Text = "";
            _areaComboBox.SelectedIndex = 0;
            _itemTypeComboBox.SelectedIndex = 0;
            _minCapacityNumeric.Value = 0;
            _maxCapacityNumeric.Value = 0;
            _minBedroomsNumeric.Value = 0;
            _maxBedroomsNumeric.Value = 0;
            _checkInDatePicker.Value = DateTime.Today;
            _checkOutDatePicker.Value = DateTime.Today.AddDays(1);
            _sortByComboBox.SelectedIndex = 0;
            _sortDescendingCheckBox.Checked = false;

            _currentCriteria = new SearchCriteria();
            _ = PerformSearch();
        }

        private async void PreviousPageButton_Click(object? sender, EventArgs e)
        {
            if (_currentCriteria.PageNumber > 1)
            {
                _currentCriteria.PageNumber--;
                await PerformSearch();
            }
        }

        private async void NextPageButton_Click(object? sender, EventArgs e)
        {
            if (_currentResults.HasNextPage)
            {
                _currentCriteria.PageNumber++;
                await PerformSearch();
            }
        }

        private void ResultsDataGridView_DoubleClick(object? sender, EventArgs e)
        {
            if (_resultsDataGridView.SelectedRows.Count > 0)
            {
                var selectedRow = _resultsDataGridView.SelectedRows[0];
                var itemId = (long)selectedRow.Cells["ID"].Value;

                // Open property details form
                var propertyForm = new PropertyDetailsForm(itemId);
                propertyForm.ShowDialog();
            }
        }

        private async Task PerformSearch()
        {
            try
            {
                // Build search criteria from form
                _currentCriteria.SearchText = string.IsNullOrWhiteSpace(_searchTextBox.Text) ? null : _searchTextBox.Text;
                _currentCriteria.AreaID = _areaComboBox.SelectedValue as long?;
                _currentCriteria.ItemTypeID = _itemTypeComboBox.SelectedValue as long?;
                _currentCriteria.MinCapacity = _minCapacityNumeric.Value > 0 ? (int)_minCapacityNumeric.Value : null;
                _currentCriteria.MaxCapacity = _maxCapacityNumeric.Value > 0 ? (int)_maxCapacityNumeric.Value : null;
                _currentCriteria.MinBedrooms = _minBedroomsNumeric.Value > 0 ? (int)_minBedroomsNumeric.Value : null;
                _currentCriteria.MaxBedrooms = _maxBedroomsNumeric.Value > 0 ? (int)_maxBedroomsNumeric.Value : null;

                // Date validation
                if (_checkInDatePicker.Value.Date >= DateTime.Today && _checkOutDatePicker.Value.Date > _checkInDatePicker.Value.Date)
                {
                    _currentCriteria.CheckInDate = _checkInDatePicker.Value.Date;
                    _currentCriteria.CheckOutDate = _checkOutDatePicker.Value.Date;
                }
                else
                {
                    _currentCriteria.CheckInDate = null;
                    _currentCriteria.CheckOutDate = null;
                }

                _currentCriteria.SortBy = _sortByComboBox.SelectedItem?.ToString() ?? "Title";
                _currentCriteria.SortDescending = _sortDescendingCheckBox.Checked;

                // Perform search
                _currentResults = await _itemRepository.SearchItemsAsync(_currentCriteria);

                // Update UI
                UpdateResultsDisplay();
                UpdatePaginationControls();
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Error performing search", ex);
                MessageBox.Show("Error performing search. Please try again.", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void UpdateResultsDisplay()
        {
            _resultsCountLabel.Text = $"{_currentResults.TotalCount} properties found";

            var displayData = _currentResults.Items.Select(i => new
            {
                ID = i.ID,
                Title = i.Title,
                Area = i.Area?.Name,
                Type = i.ItemType?.Name,
                Capacity = i.Capacity,
                Bedrooms = i.NumberOfBedrooms,
                Bathrooms = i.NumberOfBathrooms,
                Address = i.ApproximateAddress,
                Owner = i.User?.FullName,
                MinNights = i.MinimumNights,
                MaxNights = i.MaximumNights
            }).ToList();

            _resultsDataGridView.DataSource = displayData;

            // Hide ID column
            if (_resultsDataGridView.Columns["ID"] != null)
                _resultsDataGridView.Columns["ID"].Visible = false;

            // Set column headers
            if (_resultsDataGridView.Columns.Count > 0)
            {
                _resultsDataGridView.Columns["Title"]?.SetHeaderText("Property Title");
                _resultsDataGridView.Columns["Area"]?.SetHeaderText("Area");
                _resultsDataGridView.Columns["Type"]?.SetHeaderText("Property Type");
                _resultsDataGridView.Columns["Capacity"]?.SetHeaderText("Capacity");
                _resultsDataGridView.Columns["Bedrooms"]?.SetHeaderText("Bedrooms");
                _resultsDataGridView.Columns["Bathrooms"]?.SetHeaderText("Bathrooms");
                _resultsDataGridView.Columns["Address"]?.SetHeaderText("Address");
                _resultsDataGridView.Columns["Owner"]?.SetHeaderText("Owner");
                _resultsDataGridView.Columns["MinNights"]?.SetHeaderText("Min Nights");
                _resultsDataGridView.Columns["MaxNights"]?.SetHeaderText("Max Nights");
            }
        }

        private void UpdatePaginationControls()
        {
            _pageInfoLabel.Text = $"Page {_currentResults.PageNumber} of {_currentResults.TotalPages}";
            _previousPageButton.Enabled = _currentResults.HasPreviousPage;
            _nextPageButton.Enabled = _currentResults.HasNextPage;
        }
    }

    // Extension method for setting column headers
    public static class DataGridViewExtensions
    {
        public static void SetHeaderText(this DataGridViewColumn? column, string headerText)
        {
            if (column != null)
                column.HeaderText = headerText;
        }
    }
}
