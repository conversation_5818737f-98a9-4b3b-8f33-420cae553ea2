using FontAwesome.Sharp;
using SeoulStayApp.Data;
using SeoulStayApp.Models;
using SeoulStayApp.Services;
using System;
using System.Drawing;
using System.Windows.Forms;

namespace SeoulStayApp.Forms
{
    public partial class NewCreateAccountForm : Form
    {
        private UserRepository _userRepository;
        private bool _hasViewedTerms = false; // Track if user has viewed terms

        public NewCreateAccountForm()
        {
            InitializeComponent();
            _userRepository = new UserRepository();
        }
        
        private void InitializeComponent()
        {
            this.SuspendLayout();
            
            // Form properties
            this.Text = "Seoul Stay - Create Account";
            this.Size = new Size(800, 700);
            this.MinimumSize = new Size(700, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.FormBorderStyle = FormBorderStyle.Sizable;
            this.MaximizeBox = true;
            this.MinimizeBox = true;
            this.BackColor = Color.FromArgb(245, 248, 250);
            this.WindowState = FormWindowState.Normal;
            this.KeyPreview = true;
            this.KeyDown += NewCreateAccountForm_KeyDown;
            
            // Header Panel
            var headerPanel = new Panel
            {
                Location = new Point(0, 0),
                Size = new Size(800, 80),
                BackColor = Color.FromArgb(52, 73, 94)
            };
            this.Controls.Add(headerPanel);
            
            // Logo Icon
            var logoIcon = new IconButton
            {
                IconChar = IconChar.UserPlus,
                IconColor = Color.White,
                IconSize = 32,
                Location = new Point(20, 20),
                Size = new Size(50, 40),
                FlatStyle = FlatStyle.Flat,
                BackColor = Color.Transparent
            };
            logoIcon.FlatAppearance.BorderSize = 0;
            headerPanel.Controls.Add(logoIcon);
            
            // Logo Text
            var logoLabel = new Label
            {
                Text = "SEOUL STAY",
                Location = new Point(80, 25),
                Size = new Size(200, 30),
                Font = new Font("Segoe UI", 16, FontStyle.Bold),
                ForeColor = Color.White,
                BackColor = Color.Transparent
            };
            headerPanel.Controls.Add(logoLabel);
            
            // Subtitle
            var subtitleLabel = new Label
            {
                Text = "Create Your Account",
                Location = new Point(300, 30),
                Size = new Size(300, 20),
                Font = new Font("Segoe UI", 10, FontStyle.Italic),
                ForeColor = Color.FromArgb(189, 195, 199),
                BackColor = Color.Transparent
            };
            headerPanel.Controls.Add(subtitleLabel);
            
            // Main Registration Panel
            var registrationPanel = new Panel
            {
                Name = "registrationPanel",
                Location = new Point(100, 120),
                Size = new Size(600, 500),
                BackColor = Color.White,
                BorderStyle = BorderStyle.FixedSingle,
                AutoScroll = true
            };
            this.Controls.Add(registrationPanel);
            
            // Registration Title
            var registrationTitle = new Label
            {
                Text = "Your Information",
                Location = new Point(20, 20),
                Size = new Size(560, 30),
                Font = new Font("Segoe UI", 14, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94),
                TextAlign = ContentAlignment.MiddleCenter
            };
            registrationPanel.Controls.Add(registrationTitle);
            
            // Account Type Section
            var accountTypeLabel = new Label
            {
                Text = "Account Type:",
                Location = new Point(30, 70),
                Size = new Size(100, 23),
                Font = new Font("Segoe UI", 10, FontStyle.Bold),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            registrationPanel.Controls.Add(accountTypeLabel);
            
            var employeeRoleRadio = new RadioButton
            {
                Name = "employeeRoleRadio",
                Text = "Employee/Manager",
                Location = new Point(150, 70),
                Size = new Size(150, 23),
                Font = new Font("Segoe UI", 9),
                Checked = true
            };
            registrationPanel.Controls.Add(employeeRoleRadio);
            
            var userRoleRadio = new RadioButton
            {
                Name = "userRoleRadio",
                Text = "Traveler/Guest",
                Location = new Point(320, 70),
                Size = new Size(130, 23),
                Font = new Font("Segoe UI", 9)
            };
            registrationPanel.Controls.Add(userRoleRadio);
            
            // Username Section
            var usernameIcon = new IconPictureBox
            {
                IconChar = IconChar.User,
                IconColor = Color.FromArgb(52, 73, 94),
                IconSize = 20,
                Location = new Point(30, 110),
                Size = new Size(25, 25)
            };
            registrationPanel.Controls.Add(usernameIcon);
            
            var usernameLabel = new Label
            {
                Text = "Username:",
                Location = new Point(65, 110),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            registrationPanel.Controls.Add(usernameLabel);
            
            var usernameTextBox = new TextBox
            {
                Name = "usernameTextBox",
                Location = new Point(30, 135),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.FixedSingle
            };
            registrationPanel.Controls.Add(usernameTextBox);
            
            // Gender Section
            var genderLabel = new Label
            {
                Text = "Gender:",
                Location = new Point(320, 110),
                Size = new Size(60, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            registrationPanel.Controls.Add(genderLabel);
            
            var maleRadioButton = new RadioButton
            {
                Name = "maleRadioButton",
                Text = "Male",
                Location = new Point(320, 135),
                Size = new Size(60, 23),
                Font = new Font("Segoe UI", 9),
                Checked = true
            };
            registrationPanel.Controls.Add(maleRadioButton);
            
            var femaleRadioButton = new RadioButton
            {
                Name = "femaleRadioButton",
                Text = "Female",
                Location = new Point(390, 135),
                Size = new Size(70, 23),
                Font = new Font("Segoe UI", 9)
            };
            registrationPanel.Controls.Add(femaleRadioButton);
            
            // Full Name Section
            var fullNameIcon = new IconPictureBox
            {
                IconChar = IconChar.IdCard,
                IconColor = Color.FromArgb(52, 73, 94),
                IconSize = 20,
                Location = new Point(30, 175),
                Size = new Size(25, 25)
            };
            registrationPanel.Controls.Add(fullNameIcon);
            
            var fullNameLabel = new Label
            {
                Text = "Full Name:",
                Location = new Point(65, 175),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            registrationPanel.Controls.Add(fullNameLabel);
            
            var fullNameTextBox = new TextBox
            {
                Name = "fullNameTextBox",
                Location = new Point(30, 200),
                Size = new Size(250, 25),
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.FixedSingle
            };
            registrationPanel.Controls.Add(fullNameTextBox);
            
            // Family Count Section
            var familyIcon = new IconPictureBox
            {
                IconChar = IconChar.Users,
                IconColor = Color.FromArgb(52, 73, 94),
                IconSize = 20,
                Location = new Point(320, 175),
                Size = new Size(25, 25)
            };
            registrationPanel.Controls.Add(familyIcon);
            
            var familyCountLabel = new Label
            {
                Text = "Family Members:",
                Location = new Point(355, 175),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            registrationPanel.Controls.Add(familyCountLabel);
            
            var familyCountNumeric = new NumericUpDown
            {
                Name = "familyCountNumeric",
                Location = new Point(320, 200),
                Size = new Size(60, 25),
                Minimum = 1,
                Maximum = 20,
                Value = 1,
                Font = new Font("Segoe UI", 10)
            };
            registrationPanel.Controls.Add(familyCountNumeric);
            
            // Birthday Section
            var birthdayIcon = new IconPictureBox
            {
                IconChar = IconChar.Calendar,
                IconColor = Color.FromArgb(52, 73, 94),
                IconSize = 20,
                Location = new Point(30, 240),
                Size = new Size(25, 25)
            };
            registrationPanel.Controls.Add(birthdayIcon);
            
            var birthdayLabel = new Label
            {
                Text = "Birthday:",
                Location = new Point(65, 240),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            registrationPanel.Controls.Add(birthdayLabel);
            
            var birthdayDatePicker = new DateTimePicker
            {
                Name = "birthdayDatePicker",
                Location = new Point(30, 265),
                Size = new Size(200, 25),
                Format = DateTimePickerFormat.Short,
                MaxDate = DateTime.Today.AddYears(-18),
                Value = DateTime.Today.AddYears(-25),
                Font = new Font("Segoe UI", 10)
            };
            registrationPanel.Controls.Add(birthdayDatePicker);

            // Password Section
            var passwordIcon = new IconPictureBox
            {
                IconChar = IconChar.Lock,
                IconColor = Color.FromArgb(52, 73, 94),
                IconSize = 20,
                Location = new Point(30, 305),
                Size = new Size(25, 25)
            };
            registrationPanel.Controls.Add(passwordIcon);

            var passwordLabel = new Label
            {
                Text = "Password:",
                Location = new Point(65, 305),
                Size = new Size(80, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            registrationPanel.Controls.Add(passwordLabel);

            var passwordTextBox = new TextBox
            {
                Name = "passwordTextBox",
                Location = new Point(30, 330),
                Size = new Size(250, 25),
                UseSystemPasswordChar = true,
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.FixedSingle
            };
            registrationPanel.Controls.Add(passwordTextBox);

            // Retype Password Section
            var retypePasswordIcon = new IconPictureBox
            {
                IconChar = IconChar.Lock,
                IconColor = Color.FromArgb(52, 73, 94),
                IconSize = 20,
                Location = new Point(320, 305),
                Size = new Size(25, 25)
            };
            registrationPanel.Controls.Add(retypePasswordIcon);

            var retypePasswordLabel = new Label
            {
                Text = "Retype Password:",
                Location = new Point(355, 305),
                Size = new Size(120, 23),
                Font = new Font("Segoe UI", 10),
                ForeColor = Color.FromArgb(52, 73, 94)
            };
            registrationPanel.Controls.Add(retypePasswordLabel);

            var retypePasswordTextBox = new TextBox
            {
                Name = "retypePasswordTextBox",
                Location = new Point(320, 330),
                Size = new Size(250, 25),
                UseSystemPasswordChar = true,
                Font = new Font("Segoe UI", 10),
                BorderStyle = BorderStyle.FixedSingle
            };
            registrationPanel.Controls.Add(retypePasswordTextBox);

            // Terms and Conditions CheckBox
            var termsCheckBox = new CheckBox
            {
                Name = "termsCheckBox",
                Text = "I agree to the Terms and Conditions",
                Location = new Point(30, 375),
                Size = new Size(250, 23),
                Font = new Font("Segoe UI", 9)
            };
            registrationPanel.Controls.Add(termsCheckBox);

            // View Terms Link
            var viewTermsLink = new LinkLabel
            {
                Name = "viewTermsLink",
                Text = "📋 View Terms and Conditions (Required)",
                Location = new Point(280, 375),
                Size = new Size(220, 23),
                Font = new Font("Segoe UI", 9),
                LinkColor = Color.FromArgb(231, 76, 60), // Red color to indicate required
                ForeColor = Color.FromArgb(231, 76, 60)
            };
            viewTermsLink.LinkClicked += ViewTermsLink_LinkClicked;
            registrationPanel.Controls.Add(viewTermsLink);

            // Register Button
            var registerButton = new IconButton
            {
                Name = "registerButton",
                Text = "  Register & Login",
                IconChar = IconChar.UserPlus,
                IconColor = Color.White,
                IconSize = 20,
                Location = new Point(30, 420),
                Size = new Size(180, 40),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(46, 204, 113),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                ImageAlign = ContentAlignment.MiddleLeft,
                TextAlign = ContentAlignment.MiddleCenter
            };
            registerButton.FlatAppearance.BorderSize = 0;
            registerButton.Click += RegisterButton_Click;

            // Add hover effects
            registerButton.MouseEnter += (s, e) => {
                registerButton.BackColor = Color.FromArgb(39, 174, 96);
                this.Cursor = Cursors.Hand;
            };
            registerButton.MouseLeave += (s, e) => {
                registerButton.BackColor = Color.FromArgb(46, 204, 113);
                this.Cursor = Cursors.Default;
            };

            registrationPanel.Controls.Add(registerButton);

            // Return to Login Button
            var returnButton = new IconButton
            {
                Name = "returnButton",
                Text = "  Return to Login",
                IconChar = IconChar.ArrowLeft,
                IconColor = Color.White,
                IconSize = 20,
                Location = new Point(250, 420),
                Size = new Size(180, 40),
                Font = new Font("Segoe UI", 11, FontStyle.Bold),
                BackColor = Color.FromArgb(149, 165, 166),
                ForeColor = Color.White,
                FlatStyle = FlatStyle.Flat,
                ImageAlign = ContentAlignment.MiddleLeft,
                TextAlign = ContentAlignment.MiddleCenter
            };
            returnButton.FlatAppearance.BorderSize = 0;
            returnButton.Click += (s, e) => this.Close();

            // Add hover effects
            returnButton.MouseEnter += (s, e) => {
                returnButton.BackColor = Color.FromArgb(127, 140, 141);
                this.Cursor = Cursors.Hand;
            };
            returnButton.MouseLeave += (s, e) => {
                returnButton.BackColor = Color.FromArgb(149, 165, 166);
                this.Cursor = Cursors.Default;
            };

            registrationPanel.Controls.Add(returnButton);

            // Add resize event handler for responsive design
            this.Resize += NewCreateAccountForm_Resize;
            this.Load += NewCreateAccountForm_Load;

            this.ResumeLayout(false);
        }

        private void NewCreateAccountForm_Load(object? sender, EventArgs e)
        {
            // Initial layout adjustment
            AdjustLayout();
        }

        private void NewCreateAccountForm_Resize(object? sender, EventArgs e)
        {
            // Adjust layout when form is resized
            AdjustLayout();
        }

        private void AdjustLayout()
        {
            if (this.WindowState == FormWindowState.Minimized)
                return;

            // Suspend layout to prevent flickering
            this.SuspendLayout();

            try
            {
                // Get current form dimensions
                int formWidth = this.ClientSize.Width;
                int formHeight = this.ClientSize.Height;

                // Ensure minimum usable dimensions
                if (formWidth < 600 || formHeight < 500)
                    return;

            // Calculate responsive positions
            int centerX = formWidth / 2;

            // Adjust header panel width
            var headerPanel = this.Controls.OfType<Panel>().FirstOrDefault(p => p.BackColor == Color.FromArgb(52, 73, 94));
            if (headerPanel != null)
            {
                headerPanel.Size = new Size(formWidth, 80);
            }

            // Adjust main registration panel position and size
            var registrationPanel = this.Controls.Find("registrationPanel", false).FirstOrDefault() as Panel;
            if (registrationPanel != null)
            {
                int panelWidth = Math.Min(700, formWidth - 100);
                int panelHeight = Math.Min(550, formHeight - 150);

                registrationPanel.Size = new Size(panelWidth, panelHeight);
                registrationPanel.Location = new Point(centerX - panelWidth / 2, 100);
            }
            }
            finally
            {
                // Resume layout to apply changes
                this.ResumeLayout(true);
            }
        }

        private void NewCreateAccountForm_KeyDown(object? sender, KeyEventArgs e)
        {
            // Handle keyboard shortcuts
            switch (e.KeyCode)
            {
                case Keys.Escape:
                    // Escape key closes form
                    this.Close();
                    e.Handled = true;
                    break;
                case Keys.F11:
                    // F11 toggles fullscreen
                    ToggleFullscreen();
                    e.Handled = true;
                    break;
            }
        }

        private void ToggleFullscreen()
        {
            if (this.WindowState == FormWindowState.Maximized && this.FormBorderStyle == FormBorderStyle.None)
            {
                // Exit fullscreen
                this.FormBorderStyle = FormBorderStyle.Sizable;
                this.WindowState = FormWindowState.Normal;
            }
            else
            {
                // Enter fullscreen
                this.FormBorderStyle = FormBorderStyle.None;
                this.WindowState = FormWindowState.Maximized;
            }
            AdjustLayout();
        }

        private async void RegisterButton_Click(object? sender, EventArgs e)
        {
            // Get all controls
            var usernameTextBox = this.Controls.Find("usernameTextBox", true)[0] as TextBox;
            var fullNameTextBox = this.Controls.Find("fullNameTextBox", true)[0] as TextBox;
            var passwordTextBox = this.Controls.Find("passwordTextBox", true)[0] as TextBox;
            var retypePasswordTextBox = this.Controls.Find("retypePasswordTextBox", true)[0] as TextBox;
            var maleRadioButton = this.Controls.Find("maleRadioButton", true)[0] as RadioButton;
            var employeeRoleRadio = this.Controls.Find("employeeRoleRadio", true)[0] as RadioButton;
            var birthdayDatePicker = this.Controls.Find("birthdayDatePicker", true)[0] as DateTimePicker;
            var familyCountNumeric = this.Controls.Find("familyCountNumeric", true)[0] as NumericUpDown;
            var termsCheckBox = this.Controls.Find("termsCheckBox", true)[0] as CheckBox;

            // Enhanced Validation
            if (string.IsNullOrWhiteSpace(usernameTextBox!.Text) ||
                string.IsNullOrWhiteSpace(fullNameTextBox!.Text) ||
                string.IsNullOrWhiteSpace(passwordTextBox!.Text))
            {
                MessageBox.Show("Please fill in all required fields.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Validate username
            var (isValidUsername, usernameMessage) = SecurityService.ValidateUsername(usernameTextBox.Text);
            if (!isValidUsername)
            {
                MessageBox.Show(usernameMessage, "Username Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Validate password strength
            var (isValidPassword, passwordMessage) = SecurityService.ValidatePasswordStrength(passwordTextBox.Text);
            if (!isValidPassword)
            {
                MessageBox.Show(passwordMessage, "Password Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (passwordTextBox.Text != retypePasswordTextBox!.Text)
            {
                MessageBox.Show("Passwords do not match.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (!termsCheckBox!.Checked)
            {
                MessageBox.Show("You must agree to the Terms and Conditions.", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            // Check if user has viewed terms and conditions
            if (!_hasViewedTerms)
            {
                MessageBox.Show("You must view the Terms and Conditions before registering. Please click 'View Terms and Conditions' to read them first.",
                    "Terms Must Be Viewed", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            try
            {
                // Check if username already exists
                if (await _userRepository.UsernameExistsAsync(usernameTextBox.Text))
                {
                    MessageBox.Show("Username already exists. Please choose a different username.", "Registration Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Determine UserTypeID based on role selection
                int userTypeId = employeeRoleRadio!.Checked ? 1 : 2; // 1 = employee, 2 = user

                // Create new user
                var newUser = new User
                {
                    GUID = Guid.NewGuid(),
                    UserTypeID = userTypeId,
                    Username = usernameTextBox.Text,
                    FullName = fullNameTextBox.Text,
                    Gender = maleRadioButton!.Checked,
                    BirthDate = birthdayDatePicker!.Value,
                    FamilyCount = (int)familyCountNumeric!.Value
                };

                bool success = await _userRepository.CreateUserAsync(newUser, passwordTextBox.Text);

                if (success)
                {
                    MessageBox.Show("Account created successfully! You can now log in.", "Registration Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    this.DialogResult = DialogResult.OK;
                    this.Close();
                }
                else
                {
                    MessageBox.Show("Failed to create account. Please try again.", "Registration Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Registration error", ex);
                MessageBox.Show("An error occurred during registration. Please try again.", "Registration Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ViewTermsLink_LinkClicked(object? sender, LinkLabelLinkClickedEventArgs e)
        {
            // Show comprehensive terms and conditions
            var result = MessageBox.Show("SEOUL STAY - TERMS AND CONDITIONS\n\n" +
                "ACCOUNT REGISTRATION:\n" +
                "1. Users must provide accurate and complete information during registration.\n" +
                "2. Users are responsible for maintaining the confidentiality of their account credentials.\n" +
                "3. Each user may only maintain one active account.\n\n" +

                "BOOKING POLICIES:\n" +
                "4. All bookings are subject to availability and confirmation.\n" +
                "5. Payment must be completed at the time of booking confirmation.\n" +
                "6. Cancellation policies vary by property and booking type.\n" +
                "7. No-show policies may result in full charge of the booking.\n\n" +

                "USER RESPONSIBILITIES:\n" +
                "8. Users must comply with all applicable laws and regulations.\n" +
                "9. Users are responsible for any damages caused during their stay.\n" +
                "10. Inappropriate behavior may result in immediate account termination.\n\n" +

                "PRIVACY & DATA:\n" +
                "11. Personal information is collected and used in accordance with our Privacy Policy.\n" +
                "12. Seoul Stay may use booking data for service improvement and marketing.\n\n" +

                "LIABILITY & DISCLAIMERS:\n" +
                "13. Seoul Stay is not liable for indirect, incidental, or consequential damages.\n" +
                "14. Maximum liability is limited to the amount paid for services.\n" +
                "15. Users book accommodations at their own risk.\n\n" +

                "GENERAL TERMS:\n" +
                "16. Seoul Stay reserves the right to modify these terms at any time.\n" +
                "17. Disputes will be resolved through binding arbitration.\n" +
                "18. These terms are governed by applicable local laws.\n\n" +

                "By clicking 'I Agree' below, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.\n\n" +
                "Do you agree to these Terms and Conditions?",
                "Terms and Conditions - Seoul Stay", MessageBoxButtons.YesNo, MessageBoxIcon.Information);

            // Set flag that user has viewed terms
            if (result == DialogResult.Yes || result == DialogResult.No)
            {
                _hasViewedTerms = true;

                // Update the link appearance to show it's been viewed
                var viewTermsLink = this.Controls.Find("viewTermsLink", true).FirstOrDefault() as LinkLabel;
                if (viewTermsLink != null)
                {
                    viewTermsLink.Text = "✅ Terms and Conditions (Viewed)";
                    viewTermsLink.LinkColor = Color.FromArgb(46, 204, 113); // Green color to indicate viewed
                    viewTermsLink.ForeColor = Color.FromArgb(46, 204, 113);
                }

                // If user clicked Yes, also check the terms checkbox
                if (result == DialogResult.Yes)
                {
                    var termsCheckBox = this.Controls.Find("termsCheckBox", true).FirstOrDefault() as CheckBox;
                    if (termsCheckBox != null)
                    {
                        termsCheckBox.Checked = true;
                    }
                }
            }
        }
    }
}
