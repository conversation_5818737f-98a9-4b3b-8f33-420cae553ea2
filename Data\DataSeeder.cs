using Microsoft.Data.SqlClient;
using System;
using System.Threading.Tasks;

namespace SeoulStayApp.Data
{
    public class DataSeeder
    {
        public static async Task SeedUserTypesAsync()
        {
            const string checkQuery = "SELECT COUNT(*) FROM UserTypes";
            const string insertQuery = @"
                INSERT INTO UserTypes (GUID, Name) VALUES 
                (@GUID1, 'Traveler'),
                (@GUID2, 'Owner'),
                (@GUID3, 'Manager'),
                (@GUID4, 'Employee')";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                
                // Check if UserTypes already exist
                using (var checkCommand = new SqlCommand(checkQuery, connection))
                {
                    var count = (int)await checkCommand.ExecuteScalarAsync();
                    if (count > 0) return; // Already seeded
                }
                
                // Insert UserTypes
                using (var command = new SqlCommand(insertQuery, connection))
                {
                    command.Parameters.AddWithValue("@GUID1", Guid.NewGuid());
                    command.Parameters.AddWithValue("@GUID2", Guid.NewGuid());
                    command.Parameters.AddWithValue("@GUID3", Guid.NewGuid());
                    command.Parameters.AddWithValue("@GUID4", Guid.NewGuid());
                    
                    await command.ExecuteNonQueryAsync();
                }
            }
        }
        
        public static async Task SeedItemTypesAsync()
        {
            const string checkQuery = "SELECT COUNT(*) FROM ItemTypes";
            const string insertQuery = @"
                INSERT INTO ItemTypes (GUID, Name) VALUES 
                (@GUID1, 'Apartment'),
                (@GUID2, 'House'),
                (@GUID3, 'Condo'),
                (@GUID4, 'Studio'),
                (@GUID5, 'Villa')";
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                
                // Check if ItemTypes already exist
                using (var checkCommand = new SqlCommand(checkQuery, connection))
                {
                    var count = (int)await checkCommand.ExecuteScalarAsync();
                    if (count > 0) return; // Already seeded
                }
                
                // Insert ItemTypes
                using (var command = new SqlCommand(insertQuery, connection))
                {
                    command.Parameters.AddWithValue("@GUID1", Guid.NewGuid());
                    command.Parameters.AddWithValue("@GUID2", Guid.NewGuid());
                    command.Parameters.AddWithValue("@GUID3", Guid.NewGuid());
                    command.Parameters.AddWithValue("@GUID4", Guid.NewGuid());
                    command.Parameters.AddWithValue("@GUID5", Guid.NewGuid());
                    
                    await command.ExecuteNonQueryAsync();
                }
            }
        }
        
        public static async Task SeedAllAsync()
        {
            try
            {
                await SeedUserTypesAsync();
                await SeedItemTypesAsync();
            }
            catch (Exception ex)
            {
                throw new Exception($"Error seeding data: {ex.Message}", ex);
            }
        }
    }
}
