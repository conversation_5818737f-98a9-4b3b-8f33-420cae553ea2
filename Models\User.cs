using System;
using System.ComponentModel.DataAnnotations;

namespace SeoulStayApp.Models
{
    public class User
    {
        public long ID { get; set; }
        public Guid GUID { get; set; }
        public long UserTypeID { get; set; }
        
        [Required]
        [StringLength(50)]
        public string Username { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string Password { get; set; } = string.Empty;

        [Required]
        [StringLength(50)]
        public string FullName { get; set; } = string.Empty;
        
        public bool Gender { get; set; } // true = Male, false = Female
        public DateTime BirthDate { get; set; }
        public int FamilyCount { get; set; }
        
        // Navigation property
        public UserType? UserType { get; set; }
    }
}
