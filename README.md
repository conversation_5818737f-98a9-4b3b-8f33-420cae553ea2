# Seoul Stay Windows Forms Application

A Windows Forms application for managing Seoul accommodation bookings, built with .NET 9 and SQL Server.

## Features

### 1. Login Form
- Employee/User authentication
- Show/Hide password functionality
- "Keep me signed in" option
- Link to create new account

### 2. Create Account Form
- User registration with validation
- Gender selection (Male/Female)
- Birthday picker with age validation (18+)
- Family member count
- Password confirmation
- Terms and conditions acceptance

### 3. Management Form
- **Traveler Tab**: View all available properties
- **Owner/Manager Tab**: View properties owned by the logged-in user
- Property listings with details (title, area, type, capacity, etc.)
- Logout and exit functionality

## Database Schema

The application uses the existing `Session1-MsSQL.sql` database with the following main tables:
- `Users` - User accounts and profiles
- `UserTypes` - User role definitions
- `Items` - Property listings
- `ItemTypes` - Property type categories
- `Areas` - Seoul district areas
- `Amenities` - Property amenities
- And various relationship tables

## Prerequisites

1. **SQL Server** - Local or remote SQL Server instance
2. **.NET 9 SDK** - For building and running the application
3. **Windows OS** - Required for Windows Forms

## Setup Instructions

### 1. Database Setup
1. Ensure SQL Server is running
2. Execute the `Session1-MsSQL.sql` script to create the database and populate initial data
3. Update the connection string in `DatabaseConnection.cs` if needed:
   ```csharp
   _connectionString = "Server=localhost;Database=DataBase;Integrated Security=true;TrustServerCertificate=true;";
   ```

### 2. Application Setup
1. Clone or download the project
2. Open terminal in the `SeoulStayApp` directory
3. Restore dependencies:
   ```bash
   dotnet restore
   ```
4. Build the application:
   ```bash
   dotnet build
   ```
5. Run the application:
   ```bash
   dotnet run
   ```

## Usage

### First Time Setup
1. The application will automatically seed UserTypes and ItemTypes on first run
2. Create a new user account using the "Create Account" form
3. Login with your credentials

### Creating Test Data
To test the application with sample properties, you can manually insert data into the `Items` table or use the existing data from the SQL script.

### User Roles
- **Traveler**: Can view all available properties
- **Owner**: Can view their own properties
- **Manager**: Can manage properties
- **Employee**: Administrative access

## Project Structure

```
SeoulStayApp/
├── Models/           # Data models (User, Item, Area, etc.)
├── Data/            # Database access layer
│   ├── DatabaseConnection.cs
│   ├── UserRepository.cs
│   ├── ItemRepository.cs
│   └── DataSeeder.cs
├── Forms/           # Windows Forms
│   ├── LoginForm.cs
│   ├── CreateAccountForm.cs
│   └── ManagementForm.cs
└── Program.cs       # Application entry point
```

## Technologies Used

- **.NET 9** - Framework
- **Windows Forms** - UI Framework
- **Microsoft.Data.SqlClient** - Database connectivity
- **SQL Server** - Database

## Notes

- The application uses Windows Authentication by default for SQL Server
- Password storage is currently plain text (should be hashed in production)
- The application includes basic validation and error handling
- All forms are designed to match the provided wireframe specifications

## Troubleshooting

### Database Connection Issues
- Verify SQL Server is running
- Check the connection string in `DatabaseConnection.cs`
- Ensure the database exists and is accessible

### Build Issues
- Ensure .NET 9 SDK is installed
- Run `dotnet restore` to restore NuGet packages
- Check for any missing dependencies

### Runtime Issues
- Verify all required tables exist in the database
- Check that UserTypes and ItemTypes have been seeded
- Ensure proper permissions for database access
