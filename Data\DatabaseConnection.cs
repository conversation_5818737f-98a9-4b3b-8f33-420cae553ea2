using Microsoft.Data.SqlClient;
using SeoulStayApp.Services;
using System;
using System.Configuration;

namespace SeoulStayApp.Data
{
    public class DatabaseConnection
    {
        private static string? _connectionString;

        public static string ConnectionString
        {
            get
            {
                if (string.IsNullOrEmpty(_connectionString))
                {
                    // Use your SQL Server instance
                    _connectionString = "Server=YAE-IS-TIRED\\SQLSERVER2022;Database=DataBase;Integrated Security=true;TrustServerCertificate=true;";
                }
                return _connectionString;
            }
            set { _connectionString = value; }
        }
        
        public static SqlConnection GetConnection()
        {
            return new SqlConnection(ConnectionString);
        }
        
        public static bool TestConnection()
        {
            try
            {
                using (var connection = GetConnection())
                {
                    connection.Open();
                    return true;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
