using Microsoft.Data.SqlClient;
using SeoulStayApp.Models;
using SeoulStayApp.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace SeoulStayApp.Data
{
    public class ItemRepository
    {
        public async Task<List<Item>> GetItemsByUserAsync(long userId)
        {
            const string query = @"
                SELECT i.ID, i.GUID, i.UserID, i.ItemTypeID, i.AreaID, i.Title, i.Capacity, 
                       i.NumberOfBeds, i.NumberOfBedrooms, i.NumberOfBathrooms, i.ExactAddress,
                       i.ApproximateAddress, i.Description, i.HostRules, i.MinimumNights, i.MaximumNights,
                       it.Name as ItemTypeName, a.Name as AreaName
                FROM Items i
                INNER JOIN ItemTypes it ON i.ItemTypeID = it.ID
                INNER JOIN Areas a ON i.AreaID = a.ID
                WHERE i.UserID = @UserID";
            
            var items = new List<Item>();
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@UserID", userId);
                    
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            items.Add(new Item
                            {
                                ID = reader.GetInt64(0),
                                GUID = reader.GetGuid(1),
                                UserID = reader.GetInt64(2),
                                ItemTypeID = reader.GetInt64(3),
                                AreaID = reader.GetInt64(4),
                                Title = reader.GetString(5),
                                Capacity = reader.GetInt32(6),
                                NumberOfBeds = reader.GetInt32(7),
                                NumberOfBedrooms = reader.GetInt32(8),
                                NumberOfBathrooms = reader.GetInt32(9),
                                ExactAddress = reader.GetString(10),
                                ApproximateAddress = reader.GetString(11),
                                Description = reader.GetString(12),
                                HostRules = reader.GetString(13),
                                MinimumNights = reader.GetInt32(14),
                                MaximumNights = reader.GetInt32(15),
                                ItemType = new ItemType
                                {
                                    ID = reader.GetInt64(3),
                                    Name = reader.GetString(16)
                                },
                                Area = new Area
                                {
                                    ID = reader.GetInt64(4),
                                    Name = reader.GetString(17)
                                }
                            });
                        }
                    }
                }
            }
            return items;
        }
        
        public async Task<List<Item>> GetAllItemsAsync()
        {
            const string query = @"
                SELECT i.ID, i.GUID, i.UserID, i.ItemTypeID, i.AreaID, i.Title, i.Capacity, 
                       i.NumberOfBeds, i.NumberOfBedrooms, i.NumberOfBathrooms, i.ExactAddress,
                       i.ApproximateAddress, i.Description, i.HostRules, i.MinimumNights, i.MaximumNights,
                       it.Name as ItemTypeName, a.Name as AreaName, u.FullName as OwnerName
                FROM Items i
                INNER JOIN ItemTypes it ON i.ItemTypeID = it.ID
                INNER JOIN Areas a ON i.AreaID = a.ID
                INNER JOIN Users u ON i.UserID = u.ID";
            
            var items = new List<Item>();
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            items.Add(new Item
                            {
                                ID = reader.GetInt64(0),
                                GUID = reader.GetGuid(1),
                                UserID = reader.GetInt64(2),
                                ItemTypeID = reader.GetInt64(3),
                                AreaID = reader.GetInt64(4),
                                Title = reader.GetString(5),
                                Capacity = reader.GetInt32(6),
                                NumberOfBeds = reader.GetInt32(7),
                                NumberOfBedrooms = reader.GetInt32(8),
                                NumberOfBathrooms = reader.GetInt32(9),
                                ExactAddress = reader.GetString(10),
                                ApproximateAddress = reader.GetString(11),
                                Description = reader.GetString(12),
                                HostRules = reader.GetString(13),
                                MinimumNights = reader.GetInt32(14),
                                MaximumNights = reader.GetInt32(15),
                                ItemType = new ItemType
                                {
                                    ID = reader.GetInt64(3),
                                    Name = reader.GetString(16)
                                },
                                Area = new Area
                                {
                                    ID = reader.GetInt64(4),
                                    Name = reader.GetString(17)
                                },
                                User = new User
                                {
                                    ID = reader.GetInt64(2),
                                    FullName = reader.GetString(18)
                                }
                            });
                        }
                    }
                }
            }
            return items;
        }

        public async Task<Item?> GetItemByIdAsync(long itemId)
        {
            const string query = @"
                SELECT i.ID, i.GUID, i.UserID, i.ItemTypeID, i.AreaID, i.Title, i.Capacity,
                       i.NumberOfBeds, i.NumberOfBedrooms, i.NumberOfBathrooms, i.ExactAddress,
                       i.ApproximateAddress, i.Description, i.HostRules, i.MinimumNights, i.MaximumNights,
                       it.Name as ItemTypeName, a.Name as AreaName, u.FullName as UserFullName
                FROM Items i
                LEFT JOIN ItemTypes it ON i.ItemTypeID = it.ID
                LEFT JOIN Areas a ON i.AreaID = a.ID
                LEFT JOIN Users u ON i.UserID = u.ID
                WHERE i.ID = @ItemId";

            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@ItemId", itemId);
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        if (await reader.ReadAsync())
                        {
                            return new Item
                            {
                                ID = reader.GetInt64(0), // ID
                                GUID = reader.GetGuid(1), // GUID
                                UserID = reader.GetInt64(2), // UserID
                                ItemTypeID = reader.GetInt64(3), // ItemTypeID
                                AreaID = reader.GetInt64(4), // AreaID
                                Title = reader.GetString(5), // Title
                                Capacity = reader.GetInt32(6), // Capacity
                                NumberOfBeds = reader.GetInt32(7), // NumberOfBeds
                                NumberOfBedrooms = reader.GetInt32(8), // NumberOfBedrooms
                                NumberOfBathrooms = reader.GetInt32(9), // NumberOfBathrooms
                                ExactAddress = reader.GetString(10), // ExactAddress
                                ApproximateAddress = reader.GetString(11), // ApproximateAddress
                                Description = reader.GetString(12), // Description
                                HostRules = reader.GetString(13), // HostRules
                                MinimumNights = reader.GetInt32(14), // MinimumNights
                                MaximumNights = reader.GetInt32(15), // MaximumNights
                                ItemType = reader.IsDBNull(16) ? null : new ItemType { Name = reader.GetString(16) }, // ItemTypeName
                                Area = reader.IsDBNull(17) ? null : new Area { Name = reader.GetString(17) }, // AreaName
                                User = reader.IsDBNull(18) ? null : new User { FullName = reader.GetString(18) } // UserFullName
                            };
                        }
                    }
                }
            }
            return null;
        }

        public async Task<List<Area>> GetAreasAsync()
        {
            const string query = "SELECT ID, GUID, Name FROM Areas ORDER BY Name";
            var areas = new List<Area>();
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            areas.Add(new Area
                            {
                                ID = reader.GetInt64(0),
                                GUID = reader.GetGuid(1),
                                Name = reader.GetString(2)
                            });
                        }
                    }
                }
            }
            return areas;
        }
        
        public async Task<List<ItemType>> GetItemTypesAsync()
        {
            const string query = "SELECT ID, GUID, Name FROM ItemTypes ORDER BY Name";
            var itemTypes = new List<ItemType>();
            
            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    using (var reader = await command.ExecuteReaderAsync())
                    {
                        while (await reader.ReadAsync())
                        {
                            itemTypes.Add(new ItemType
                            {
                                ID = reader.GetInt64(0),
                                GUID = reader.GetGuid(1),
                                Name = reader.GetString(2)
                            });
                        }
                    }
                }
            }
            return itemTypes;
        }

        public async Task<long> AddItemAsync(Item item)
        {
            const string query = @"
                INSERT INTO Items (GUID, UserID, ItemTypeID, AreaID, Title, Capacity,
                                 NumberOfBeds, NumberOfBedrooms, NumberOfBathrooms,
                                 ExactAddress, ApproximateAddress, Description,
                                 HostRules, MinimumNights, MaximumNights)
                OUTPUT INSERTED.ID
                VALUES (@GUID, @UserID, @ItemTypeID, @AreaID, @Title, @Capacity,
                        @NumberOfBeds, @NumberOfBedrooms, @NumberOfBathrooms,
                        @ExactAddress, @ApproximateAddress, @Description,
                        @HostRules, @MinimumNights, @MaximumNights)";

            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@GUID", item.GUID == Guid.Empty ? Guid.NewGuid() : item.GUID);
                    command.Parameters.AddWithValue("@UserID", item.UserID);
                    command.Parameters.AddWithValue("@ItemTypeID", item.ItemTypeID);
                    command.Parameters.AddWithValue("@AreaID", item.AreaID);
                    command.Parameters.AddWithValue("@Title", item.Title);
                    command.Parameters.AddWithValue("@Capacity", item.Capacity);
                    command.Parameters.AddWithValue("@NumberOfBeds", item.NumberOfBeds);
                    command.Parameters.AddWithValue("@NumberOfBedrooms", item.NumberOfBedrooms);
                    command.Parameters.AddWithValue("@NumberOfBathrooms", item.NumberOfBathrooms);
                    command.Parameters.AddWithValue("@ExactAddress", item.ExactAddress);
                    command.Parameters.AddWithValue("@ApproximateAddress", item.ApproximateAddress);
                    command.Parameters.AddWithValue("@Description", item.Description);
                    command.Parameters.AddWithValue("@HostRules", item.HostRules);
                    command.Parameters.AddWithValue("@MinimumNights", item.MinimumNights);
                    command.Parameters.AddWithValue("@MaximumNights", item.MaximumNights);

                    var result = await command.ExecuteScalarAsync();
                    return (long)result;
                }
            }
        }

        public async Task UpdateItemAsync(Item item)
        {
            const string query = @"
                UPDATE Items
                SET ItemTypeID = @ItemTypeID, AreaID = @AreaID, Title = @Title,
                    Capacity = @Capacity, NumberOfBeds = @NumberOfBeds,
                    NumberOfBedrooms = @NumberOfBedrooms, NumberOfBathrooms = @NumberOfBathrooms,
                    ExactAddress = @ExactAddress, ApproximateAddress = @ApproximateAddress,
                    Description = @Description, HostRules = @HostRules,
                    MinimumNights = @MinimumNights, MaximumNights = @MaximumNights
                WHERE ID = @ID";

            using (var connection = DatabaseConnection.GetConnection())
            {
                await connection.OpenAsync();
                using (var command = new SqlCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@ID", item.ID);
                    command.Parameters.AddWithValue("@ItemTypeID", item.ItemTypeID);
                    command.Parameters.AddWithValue("@AreaID", item.AreaID);
                    command.Parameters.AddWithValue("@Title", item.Title);
                    command.Parameters.AddWithValue("@Capacity", item.Capacity);
                    command.Parameters.AddWithValue("@NumberOfBeds", item.NumberOfBeds);
                    command.Parameters.AddWithValue("@NumberOfBedrooms", item.NumberOfBedrooms);
                    command.Parameters.AddWithValue("@NumberOfBathrooms", item.NumberOfBathrooms);
                    command.Parameters.AddWithValue("@ExactAddress", item.ExactAddress);
                    command.Parameters.AddWithValue("@ApproximateAddress", item.ApproximateAddress);
                    command.Parameters.AddWithValue("@Description", item.Description);
                    command.Parameters.AddWithValue("@HostRules", item.HostRules);
                    command.Parameters.AddWithValue("@MinimumNights", item.MinimumNights);
                    command.Parameters.AddWithValue("@MaximumNights", item.MaximumNights);

                    await command.ExecuteNonQueryAsync();
                }
            }
        }

        public async Task<SearchResult<Item>> SearchItemsAsync(SearchCriteria criteria)
        {
            try
            {
                LoggingService.LogInfo($"Searching items with criteria: {criteria.SearchText}");

                var whereConditions = new List<string>();
                var parameters = new List<SqlParameter>();

                // Build WHERE clause based on criteria
                if (!string.IsNullOrWhiteSpace(criteria.SearchText))
                {
                    whereConditions.Add("(i.Title LIKE @SearchText OR i.Description LIKE @SearchText OR i.ApproximateAddress LIKE @SearchText)");
                    parameters.Add(new SqlParameter("@SearchText", $"%{criteria.SearchText}%"));
                }

                if (criteria.AreaID.HasValue)
                {
                    whereConditions.Add("i.AreaID = @AreaID");
                    parameters.Add(new SqlParameter("@AreaID", criteria.AreaID.Value));
                }

                if (criteria.ItemTypeID.HasValue)
                {
                    whereConditions.Add("i.ItemTypeID = @ItemTypeID");
                    parameters.Add(new SqlParameter("@ItemTypeID", criteria.ItemTypeID.Value));
                }

                if (criteria.MinCapacity.HasValue)
                {
                    whereConditions.Add("i.Capacity >= @MinCapacity");
                    parameters.Add(new SqlParameter("@MinCapacity", criteria.MinCapacity.Value));
                }

                if (criteria.MaxCapacity.HasValue)
                {
                    whereConditions.Add("i.Capacity <= @MaxCapacity");
                    parameters.Add(new SqlParameter("@MaxCapacity", criteria.MaxCapacity.Value));
                }

                if (criteria.MinBedrooms.HasValue)
                {
                    whereConditions.Add("i.NumberOfBedrooms >= @MinBedrooms");
                    parameters.Add(new SqlParameter("@MinBedrooms", criteria.MinBedrooms.Value));
                }

                if (criteria.MaxBedrooms.HasValue)
                {
                    whereConditions.Add("i.NumberOfBedrooms <= @MaxBedrooms");
                    parameters.Add(new SqlParameter("@MaxBedrooms", criteria.MaxBedrooms.Value));
                }

                if (criteria.MinBathrooms.HasValue)
                {
                    whereConditions.Add("i.NumberOfBathrooms >= @MinBathrooms");
                    parameters.Add(new SqlParameter("@MinBathrooms", criteria.MinBathrooms.Value));
                }

                if (criteria.MaxBathrooms.HasValue)
                {
                    whereConditions.Add("i.NumberOfBathrooms <= @MaxBathrooms");
                    parameters.Add(new SqlParameter("@MaxBathrooms", criteria.MaxBathrooms.Value));
                }

                // Check availability if dates are provided
                if (criteria.CheckInDate.HasValue && criteria.CheckOutDate.HasValue)
                {
                    whereConditions.Add(@"
                        NOT EXISTS (
                            SELECT 1 FROM Bookings b
                            WHERE b.ItemID = i.ID
                            AND b.Status IN (0, 1, 2)
                            AND (
                                (@CheckIn >= b.CheckInDate AND @CheckIn < b.CheckOutDate) OR
                                (@CheckOut > b.CheckInDate AND @CheckOut <= b.CheckOutDate) OR
                                (@CheckIn <= b.CheckInDate AND @CheckOut >= b.CheckOutDate)
                            )
                        )");
                    parameters.Add(new SqlParameter("@CheckIn", criteria.CheckInDate.Value));
                    parameters.Add(new SqlParameter("@CheckOut", criteria.CheckOutDate.Value));
                }

                var whereClause = whereConditions.Count > 0 ? "WHERE " + string.Join(" AND ", whereConditions) : "";

                // Build ORDER BY clause
                var orderBy = criteria.SortBy switch
                {
                    "Area" => "a.Name",
                    "Type" => "it.Name",
                    "Capacity" => "i.Capacity",
                    "Bedrooms" => "i.NumberOfBedrooms",
                    "Bathrooms" => "i.NumberOfBathrooms",
                    _ => "i.Title"
                };

                var sortDirection = criteria.SortDescending ? "DESC" : "ASC";

                // Count query
                var countQuery = $@"
                    SELECT COUNT(*)
                    FROM Items i
                    INNER JOIN ItemTypes it ON i.ItemTypeID = it.ID
                    INNER JOIN Areas a ON i.AreaID = a.ID
                    INNER JOIN Users u ON i.UserID = u.ID
                    {whereClause}";

                // Data query with pagination
                var dataQuery = $@"
                    SELECT i.ID, i.GUID, i.UserID, i.ItemTypeID, i.AreaID, i.Title, i.Capacity,
                           i.NumberOfBeds, i.NumberOfBedrooms, i.NumberOfBathrooms, i.ExactAddress,
                           i.ApproximateAddress, i.Description, i.HostRules, i.MinimumNights, i.MaximumNights,
                           it.Name as ItemTypeName, a.Name as AreaName, u.FullName as OwnerName
                    FROM Items i
                    INNER JOIN ItemTypes it ON i.ItemTypeID = it.ID
                    INNER JOIN Areas a ON i.AreaID = a.ID
                    INNER JOIN Users u ON i.UserID = u.ID
                    {whereClause}
                    ORDER BY {orderBy} {sortDirection}
                    OFFSET @Offset ROWS
                    FETCH NEXT @PageSize ROWS ONLY";

                var result = new SearchResult<Item>
                {
                    PageNumber = criteria.PageNumber,
                    PageSize = criteria.PageSize
                };

                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();

                // Get total count
                using (var countCommand = new SqlCommand(countQuery, connection))
                {
                    countCommand.Parameters.AddRange(parameters.ToArray());
                    result.TotalCount = (int)await countCommand.ExecuteScalarAsync();
                }

                // Get data
                using (var dataCommand = new SqlCommand(dataQuery, connection))
                {
                    dataCommand.Parameters.AddRange(parameters.ToArray());
                    dataCommand.Parameters.Add(new SqlParameter("@Offset", (criteria.PageNumber - 1) * criteria.PageSize));
                    dataCommand.Parameters.Add(new SqlParameter("@PageSize", criteria.PageSize));

                    using var reader = await dataCommand.ExecuteReaderAsync();
                    while (await reader.ReadAsync())
                    {
                        result.Items.Add(new Item
                        {
                            ID = reader.GetInt64(0),
                            GUID = reader.GetGuid(1),
                            UserID = reader.GetInt64(2),
                            ItemTypeID = reader.GetInt64(3),
                            AreaID = reader.GetInt64(4),
                            Title = reader.GetString(5),
                            Capacity = reader.GetInt32(6),
                            NumberOfBeds = reader.GetInt32(7),
                            NumberOfBedrooms = reader.GetInt32(8),
                            NumberOfBathrooms = reader.GetInt32(9),
                            ExactAddress = reader.GetString(10),
                            ApproximateAddress = reader.GetString(11),
                            Description = reader.GetString(12),
                            HostRules = reader.GetString(13),
                            MinimumNights = reader.GetInt32(14),
                            MaximumNights = reader.GetInt32(15),
                            ItemType = new ItemType
                            {
                                ID = reader.GetInt64(3),
                                Name = reader.GetString(16)
                            },
                            Area = new Area
                            {
                                ID = reader.GetInt64(4),
                                Name = reader.GetString(17)
                            },
                            User = new User
                            {
                                ID = reader.GetInt64(2),
                                FullName = reader.GetString(18)
                            }
                        });
                    }
                }

                LoggingService.LogInfo($"Search completed: {result.Items.Count} items found out of {result.TotalCount} total");
                return result;
            }
            catch (Exception ex)
            {
                LoggingService.LogError("Error searching items", ex);
                throw;
            }
        }
    }
}
