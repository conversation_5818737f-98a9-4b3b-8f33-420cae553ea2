using Microsoft.Data.SqlClient;
using SeoulStayApp.Models;
using SeoulStayApp.Services;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace SeoulStayApp.Data
{
    public class BookingRepository
    {
        public async Task<bool> CreateBookingAsync(Booking booking)
        {
            try
            {
                LoggingService.LogInfo($"Creating booking for item {booking.ItemID} by user {booking.GuestUserID}");
                
                const string query = @"
                    INSERT INTO Bookings (GUID, ItemID, GuestUserID, CheckInDate, CheckOutDate, 
                                        NumberOfGuests, SpecialRequests, TotalAmount, Status, BookingDate)
                    VALUES (@GUID, @ItemID, @GuestUserID, @CheckInDate, @CheckOutDate, 
                           @NumberOfGuests, @SpecialRequests, @TotalAmount, @Status, @BookingDate)";
                
                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();
                using var command = new SqlCommand(query, connection);
                
                command.Parameters.Add("@GUID", System.Data.SqlDbType.UniqueIdentifier).Value = booking.GUID;
                command.Parameters.Add("@ItemID", System.Data.SqlDbType.BigInt).Value = booking.ItemID;
                command.Parameters.Add("@GuestUserID", System.Data.SqlDbType.BigInt).Value = booking.GuestUserID;
                command.Parameters.Add("@CheckInDate", System.Data.SqlDbType.Date).Value = booking.CheckInDate;
                command.Parameters.Add("@CheckOutDate", System.Data.SqlDbType.Date).Value = booking.CheckOutDate;
                command.Parameters.Add("@NumberOfGuests", System.Data.SqlDbType.Int).Value = booking.NumberOfGuests;
                command.Parameters.Add("@SpecialRequests", System.Data.SqlDbType.NVarChar, 1000).Value = booking.SpecialRequests ?? (object)DBNull.Value;
                command.Parameters.Add("@TotalAmount", System.Data.SqlDbType.Decimal).Value = booking.TotalAmount;
                command.Parameters.Add("@Status", System.Data.SqlDbType.Int).Value = (int)booking.Status;
                command.Parameters.Add("@BookingDate", System.Data.SqlDbType.DateTime).Value = booking.BookingDate;
                
                var result = await command.ExecuteNonQueryAsync();
                
                if (result > 0)
                {
                    LoggingService.LogInfo($"Successfully created booking for item {booking.ItemID}");
                    return true;
                }
                
                LoggingService.LogWarning($"Failed to create booking for item {booking.ItemID}");
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error creating booking for item {booking.ItemID}", ex);
                throw;
            }
        }
        
        public async Task<List<Booking>> GetBookingsByUserAsync(long userId)
        {
            try
            {
                const string query = @"
                    SELECT b.ID, b.GUID, b.ItemID, b.GuestUserID, b.CheckInDate, b.CheckOutDate,
                           b.NumberOfGuests, b.SpecialRequests, b.TotalAmount, b.Status, b.BookingDate,
                           b.ConfirmationDate, b.CancellationDate, b.CancellationReason,
                           i.Title as ItemTitle, i.ApproximateAddress
                    FROM Bookings b
                    INNER JOIN Items i ON b.ItemID = i.ID
                    WHERE b.GuestUserID = @UserId
                    ORDER BY b.BookingDate DESC";
                
                var bookings = new List<Booking>();
                
                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();
                using var command = new SqlCommand(query, connection);
                command.Parameters.Add("@UserId", System.Data.SqlDbType.BigInt).Value = userId;
                
                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    bookings.Add(new Booking
                    {
                        ID = reader.GetInt64(0),
                        GUID = reader.GetGuid(1),
                        ItemID = reader.GetInt64(2),
                        GuestUserID = reader.GetInt64(3),
                        CheckInDate = reader.GetDateTime(4),
                        CheckOutDate = reader.GetDateTime(5),
                        NumberOfGuests = reader.GetInt32(6),
                        SpecialRequests = reader.IsDBNull(7) ? null : reader.GetString(7),
                        TotalAmount = reader.GetDecimal(8),
                        Status = (BookingStatus)reader.GetInt32(9),
                        BookingDate = reader.GetDateTime(10),
                        ConfirmationDate = reader.IsDBNull(11) ? null : reader.GetDateTime(11),
                        CancellationDate = reader.IsDBNull(12) ? null : reader.GetDateTime(12),
                        CancellationReason = reader.IsDBNull(13) ? null : reader.GetString(13),
                        Item = new Item
                        {
                            ID = reader.GetInt64(2),
                            Title = reader.GetString(14),
                            ApproximateAddress = reader.GetString(15)
                        }
                    });
                }
                
                return bookings;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting bookings for user {userId}", ex);
                throw;
            }
        }
        
        public async Task<List<Booking>> GetBookingsByPropertyOwnerAsync(long ownerId)
        {
            try
            {
                const string query = @"
                    SELECT b.ID, b.GUID, b.ItemID, b.GuestUserID, b.CheckInDate, b.CheckOutDate,
                           b.NumberOfGuests, b.SpecialRequests, b.TotalAmount, b.Status, b.BookingDate,
                           b.ConfirmationDate, b.CancellationDate, b.CancellationReason,
                           i.Title as ItemTitle, i.ApproximateAddress,
                           u.FullName as GuestName, u.Username as GuestUsername
                    FROM Bookings b
                    INNER JOIN Items i ON b.ItemID = i.ID
                    INNER JOIN Users u ON b.GuestUserID = u.ID
                    WHERE i.UserID = @OwnerId
                    ORDER BY b.BookingDate DESC";
                
                var bookings = new List<Booking>();
                
                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();
                using var command = new SqlCommand(query, connection);
                command.Parameters.Add("@OwnerId", System.Data.SqlDbType.BigInt).Value = ownerId;
                
                using var reader = await command.ExecuteReaderAsync();
                while (await reader.ReadAsync())
                {
                    bookings.Add(new Booking
                    {
                        ID = reader.GetInt64(0),
                        GUID = reader.GetGuid(1),
                        ItemID = reader.GetInt64(2),
                        GuestUserID = reader.GetInt64(3),
                        CheckInDate = reader.GetDateTime(4),
                        CheckOutDate = reader.GetDateTime(5),
                        NumberOfGuests = reader.GetInt32(6),
                        SpecialRequests = reader.IsDBNull(7) ? null : reader.GetString(7),
                        TotalAmount = reader.GetDecimal(8),
                        Status = (BookingStatus)reader.GetInt32(9),
                        BookingDate = reader.GetDateTime(10),
                        ConfirmationDate = reader.IsDBNull(11) ? null : reader.GetDateTime(11),
                        CancellationDate = reader.IsDBNull(12) ? null : reader.GetDateTime(12),
                        CancellationReason = reader.IsDBNull(13) ? null : reader.GetString(13),
                        Item = new Item
                        {
                            ID = reader.GetInt64(2),
                            Title = reader.GetString(14),
                            ApproximateAddress = reader.GetString(15)
                        },
                        GuestUser = new User
                        {
                            ID = reader.GetInt64(3),
                            FullName = reader.GetString(16),
                            Username = reader.GetString(17)
                        }
                    });
                }
                
                return bookings;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error getting bookings for owner {ownerId}", ex);
                throw;
            }
        }
        
        public async Task<bool> UpdateBookingStatusAsync(long bookingId, BookingStatus status, string? reason = null)
        {
            try
            {
                LoggingService.LogInfo($"Updating booking {bookingId} status to {status}");
                
                string query = @"
                    UPDATE Bookings 
                    SET Status = @Status";
                
                if (status == BookingStatus.Confirmed)
                {
                    query += ", ConfirmationDate = @Date";
                }
                else if (status == BookingStatus.Cancelled)
                {
                    query += ", CancellationDate = @Date, CancellationReason = @Reason";
                }
                
                query += " WHERE ID = @BookingId";
                
                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();
                using var command = new SqlCommand(query, connection);
                
                command.Parameters.Add("@Status", System.Data.SqlDbType.Int).Value = (int)status;
                command.Parameters.Add("@BookingId", System.Data.SqlDbType.BigInt).Value = bookingId;
                
                if (status == BookingStatus.Confirmed || status == BookingStatus.Cancelled)
                {
                    command.Parameters.Add("@Date", System.Data.SqlDbType.DateTime).Value = DateTime.Now;
                }
                
                if (status == BookingStatus.Cancelled)
                {
                    command.Parameters.Add("@Reason", System.Data.SqlDbType.NVarChar, 500).Value = reason ?? (object)DBNull.Value;
                }
                
                var result = await command.ExecuteNonQueryAsync();
                
                if (result > 0)
                {
                    LoggingService.LogInfo($"Successfully updated booking {bookingId} status to {status}");
                    return true;
                }
                
                LoggingService.LogWarning($"Failed to update booking {bookingId} status");
                return false;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error updating booking {bookingId} status", ex);
                throw;
            }
        }
        
        public async Task<bool> IsPropertyAvailableAsync(long itemId, DateTime checkIn, DateTime checkOut, long? excludeBookingId = null)
        {
            try
            {
                string query = @"
                    SELECT COUNT(*) 
                    FROM Bookings 
                    WHERE ItemID = @ItemId 
                    AND Status IN (0, 1, 2) -- Pending, Confirmed, CheckedIn
                    AND (
                        (@CheckIn >= CheckInDate AND @CheckIn < CheckOutDate) OR
                        (@CheckOut > CheckInDate AND @CheckOut <= CheckOutDate) OR
                        (@CheckIn <= CheckInDate AND @CheckOut >= CheckOutDate)
                    )";
                
                if (excludeBookingId.HasValue)
                {
                    query += " AND ID != @ExcludeBookingId";
                }
                
                using var connection = DatabaseConnection.GetConnection();
                await connection.OpenAsync();
                using var command = new SqlCommand(query, connection);
                
                command.Parameters.Add("@ItemId", System.Data.SqlDbType.BigInt).Value = itemId;
                command.Parameters.Add("@CheckIn", System.Data.SqlDbType.Date).Value = checkIn;
                command.Parameters.Add("@CheckOut", System.Data.SqlDbType.Date).Value = checkOut;
                
                if (excludeBookingId.HasValue)
                {
                    command.Parameters.Add("@ExcludeBookingId", System.Data.SqlDbType.BigInt).Value = excludeBookingId.Value;
                }
                
                var count = (int)await command.ExecuteScalarAsync();
                return count == 0;
            }
            catch (Exception ex)
            {
                LoggingService.LogError($"Error checking availability for item {itemId}", ex);
                throw;
            }
        }
    }
}
