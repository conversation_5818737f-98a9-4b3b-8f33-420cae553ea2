using System;
using System.ComponentModel.DataAnnotations;

namespace SeoulStayApp.Models
{
    public enum BookingStatus
    {
        Pending = 0,
        Confirmed = 1,
        CheckedIn = 2,
        CheckedOut = 3,
        Cancelled = 4,
        Completed = 5
    }
    
    public class Booking
    {
        public long ID { get; set; }
        public Guid GUID { get; set; }
        public long ItemID { get; set; }
        public long GuestUserID { get; set; }
        
        public DateTime CheckInDate { get; set; }
        public DateTime CheckOutDate { get; set; }
        public int NumberOfGuests { get; set; }
        
        [StringLength(1000)]
        public string? SpecialRequests { get; set; }
        
        public decimal TotalAmount { get; set; }
        public BookingStatus Status { get; set; }
        
        public DateTime BookingDate { get; set; }
        public DateTime? ConfirmationDate { get; set; }
        public DateTime? CancellationDate { get; set; }
        
        [StringLength(500)]
        public string? CancellationReason { get; set; }
        
        // Navigation properties
        public Item? Item { get; set; }
        public User? GuestUser { get; set; }
    }
}
