﻿[2025-07-08 00:00:27.886] [INFO    ] [Thread-01] Creating new user: Ya<PERSON>
[2025-07-08 00:00:28.185] [INFO    ] [Thread-01] Successfully created user: Ya<PERSON>
[2025-07-08 00:00:47.685] [INFO    ] [Thread-01] Authentication attempt for username: Guest
[2025-07-08 00:00:47.710] [WARNING ] [Thread-01] Failed authentication attempt for username: Guest
[2025-07-08 00:00:47.710] [WARNING ] [Thread-01] Failed login attempt for username: Guest
[2025-07-08 00:00:56.897] [INFO    ] [Thread-01] Authentication attempt for username: Guest
[2025-07-08 00:00:56.901] [WARNING ] [Thread-01] Failed authentication attempt for username: Guest
[2025-07-08 00:00:56.901] [WARNING ] [Thread-01] Failed login attempt for username: Guest
[2025-07-08 01:25:27.336] [INFO    ] [Thread-01] Creating new user: Yaze1
[2025-07-08 01:25:27.641] [INFO    ] [Thread-01] Successfully created user: Yaze1
[2025-07-08 01:26:14.753] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 01:26:14.985] [INFO    ] [Thread-01] Successful authentication for user: Yaze1
[2025-07-08 01:26:14.987] [INFO    ] [Thread-01] Session started for user: Yaze1 (ID: 10)
[2025-07-08 01:26:14.988] [INFO    ] [Thread-01] User Yaze1 logged in successfully as Employee/Manager
[2025-07-08 01:26:59.579] [INFO    ] [Thread-01] Searching items with criteria: 
[2025-07-08 01:26:59.673] [ERROR   ] [Thread-01] Error searching items
Exception: SqlException: Invalid object name 'Bookings'.
StackTrace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SeoulStayApp.Data.ItemRepository.SearchItemsAsync(SearchCriteria criteria) in C:\Test_Monday\SeoulStayApp\Data\ItemRepository.cs:line 326
[2025-07-08 01:26:59.681] [ERROR   ] [Thread-01] Error performing search
Exception: SqlException: Invalid object name 'Bookings'.
StackTrace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SeoulStayApp.Data.ItemRepository.SearchItemsAsync(SearchCriteria criteria) in C:\Test_Monday\SeoulStayApp\Data\ItemRepository.cs:line 326
   at SeoulStayApp.Forms.SearchForm.PerformSearch() in C:\Test_Monday\SeoulStayApp\Forms\SearchForm.cs:line 474
[2025-07-08 01:27:11.390] [INFO    ] [Thread-01] Session ended for user: Yaze1 (ID: 10)
[2025-07-08 01:27:11.390] [INFO    ] [Thread-01] User Yaze1 logged out
[2025-07-08 01:30:22.616] [INFO    ] [Thread-01] Creating new user: Yaze2
[2025-07-08 01:30:22.917] [INFO    ] [Thread-01] Successfully created user: Yaze2
[2025-07-08 01:38:58.819] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 01:38:59.158] [WARNING ] [Thread-01] Failed authentication attempt for username: Yaze1
[2025-07-08 01:39:03.233] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 01:39:03.471] [INFO    ] [Thread-01] Successful authentication for user: Yaze1
[2025-07-08 01:39:03.473] [INFO    ] [Thread-01] Session started for user: Yaze1 (ID: 10)
[2025-07-08 01:39:03.473] [INFO    ] [Thread-01] User Yaze1 logged in successfully as Employee/Manager
[2025-07-08 01:39:07.695] [INFO    ] [Thread-01] Searching items with criteria: 
[2025-07-08 01:39:07.787] [ERROR   ] [Thread-01] Error searching items
Exception: SqlException: Invalid object name 'Bookings'.
StackTrace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SeoulStayApp.Data.ItemRepository.SearchItemsAsync(SearchCriteria criteria) in C:\Test_Monday\SeoulStayApp\Data\ItemRepository.cs:line 326
[2025-07-08 01:39:07.794] [ERROR   ] [Thread-01] Error performing search
Exception: SqlException: Invalid object name 'Bookings'.
StackTrace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SeoulStayApp.Data.ItemRepository.SearchItemsAsync(SearchCriteria criteria) in C:\Test_Monday\SeoulStayApp\Data\ItemRepository.cs:line 326
   at SeoulStayApp.Forms.SearchForm.PerformSearch() in C:\Test_Monday\SeoulStayApp\Forms\SearchForm.cs:line 474
[2025-07-08 01:40:40.561] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 01:40:40.888] [INFO    ] [Thread-01] Successful authentication for user: Yaze1
[2025-07-08 01:40:40.890] [INFO    ] [Thread-01] Session started for user: Yaze1 (ID: 10)
[2025-07-08 01:40:40.890] [INFO    ] [Thread-01] User Yaze1 logged in successfully as Employee/Manager
[2025-07-08 01:44:48.827] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 01:44:49.118] [INFO    ] [Thread-01] Successful authentication for user: Yaze1
[2025-07-08 01:44:49.120] [INFO    ] [Thread-01] Session started for user: Yaze1 (ID: 10)
[2025-07-08 01:44:49.120] [INFO    ] [Thread-01] User Yaze1 logged in successfully as Employee/Manager
[2025-07-08 02:06:56.260] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 02:06:56.607] [WARNING ] [Thread-01] Failed authentication attempt for username: Yaze1
[2025-07-08 02:07:00.708] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 02:07:00.927] [INFO    ] [Thread-01] Successful authentication for user: Yaze1
[2025-07-08 02:07:00.929] [INFO    ] [Thread-01] Session started for user: Yaze1 (ID: 10)
[2025-07-08 02:07:00.929] [INFO    ] [Thread-01] User Yaze1 logged in successfully as Employee/Manager
[2025-07-08 02:09:37.987] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 02:09:38.273] [WARNING ] [Thread-01] Failed authentication attempt for username: Yaze1
[2025-07-08 02:09:46.965] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 02:09:47.186] [INFO    ] [Thread-01] Successful authentication for user: Yaze1
[2025-07-08 02:09:47.188] [INFO    ] [Thread-01] Session started for user: Yaze1 (ID: 10)
[2025-07-08 02:09:47.188] [INFO    ] [Thread-01] User Yaze1 logged in successfully as Employee/Manager
[2025-07-08 02:12:49.334] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 02:12:49.704] [INFO    ] [Thread-01] Successful authentication for user: Yaze1
[2025-07-08 02:12:49.706] [INFO    ] [Thread-01] Session started for user: Yaze1 (ID: 10)
[2025-07-08 02:12:49.706] [INFO    ] [Thread-01] User Yaze1 logged in successfully as Employee/Manager
[2025-07-08 02:18:45.768] [INFO    ] [Thread-01] Authentication attempt for username: Yaze1
[2025-07-08 02:18:46.115] [INFO    ] [Thread-01] Successful authentication for user: Yaze1
[2025-07-08 02:18:46.117] [INFO    ] [Thread-01] Session started for user: Yaze1 (ID: 10)
[2025-07-08 02:18:46.118] [INFO    ] [Thread-01] User Yaze1 logged in successfully as Employee/Manager
[2025-07-08 02:19:15.127] [INFO    ] [Thread-01] Searching items with criteria: 
[2025-07-08 02:19:15.220] [ERROR   ] [Thread-01] Error searching items
Exception: SqlException: Invalid object name 'Bookings'.
StackTrace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SeoulStayApp.Data.ItemRepository.SearchItemsAsync(SearchCriteria criteria) in C:\Test_Monday\SeoulStayApp\Data\ItemRepository.cs:line 455
[2025-07-08 02:19:15.228] [ERROR   ] [Thread-01] Error performing search
Exception: SqlException: Invalid object name 'Bookings'.
StackTrace:    at Microsoft.Data.SqlClient.SqlConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.SqlInternalConnection.OnError(SqlException exception, Boolean breakConnection, Action`1 wrapCloseInAction)
   at Microsoft.Data.SqlClient.TdsParser.ThrowExceptionAndWarning(TdsParserStateObject stateObj, SqlCommand command, Boolean callerHasConnectionLock, Boolean asyncClose)
   at Microsoft.Data.SqlClient.TdsParser.TryRun(RunBehavior runBehavior, SqlCommand cmdHandler, SqlDataReader dataStream, BulkCopySimpleResultSet bulkCopyHandler, TdsParserStateObject stateObj, Boolean& dataReady)
   at Microsoft.Data.SqlClient.SqlDataReader.TryConsumeMetaData()
   at Microsoft.Data.SqlClient.SqlDataReader.get_MetaData()
   at Microsoft.Data.SqlClient.SqlCommand.FinishExecuteReader(SqlDataReader ds, RunBehavior runBehavior, String resetOptionsString, Boolean isInternal, Boolean forDescribeParameterEncryption, Boolean shouldCacheForAlwaysEncrypted)
   at Microsoft.Data.SqlClient.SqlCommand.CompleteAsyncExecuteReader(Boolean isInternal, Boolean forDescribeParameterEncryption)
   at Microsoft.Data.SqlClient.SqlCommand.InternalEndExecuteReader(IAsyncResult asyncResult, Boolean isInternal, String endMethod)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderInternal(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.EndExecuteReaderAsync(IAsyncResult asyncResult)
   at Microsoft.Data.SqlClient.SqlCommand.<>c.<InternalExecuteReaderAsync>b__201_1(IAsyncResult asyncResult)
   at System.Threading.Tasks.TaskFactory`1.FromAsyncCoreLogic(IAsyncResult iar, Func`2 endFunction, Action`1 endAction, Task`1 promise, Boolean requiresSynchronization)
--- End of stack trace from previous location ---
   at SeoulStayApp.Data.ItemRepository.SearchItemsAsync(SearchCriteria criteria) in C:\Test_Monday\SeoulStayApp\Data\ItemRepository.cs:line 455
   at SeoulStayApp.Forms.SearchForm.PerformSearch() in C:\Test_Monday\SeoulStayApp\Forms\SearchForm.cs:line 474
